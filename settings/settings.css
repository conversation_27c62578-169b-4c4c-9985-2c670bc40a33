/* 设置页面样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
    color: #202124;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #065fd4 0%, #1976d2 100%);
    color: white;
    padding: 24px 32px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo svg {
    fill: white;
}

.logo h1 {
    font-size: 24px;
    font-weight: 600;
}

.version {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 14px;
}

/* 导航标签样式 */
.tabs {
    display: flex;
    background: #f1f3f4;
    border-bottom: 1px solid #dadce0;
}

.tab-button {
    background: none;
    border: none;
    padding: 16px 24px;
    font-size: 14px;
    font-weight: 500;
    color: #5f6368;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: #e8eaed;
    color: #202124;
}

.tab-button.active {
    color: #1976d2;
    border-bottom-color: #1976d2;
    background: white;
}

/* 主要内容区域 */
.main-content {
    padding: 32px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 区块样式 */
.section {
    margin-bottom: 32px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dadce0;
}

.section h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #202124;
}

.section h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #202124;
}

.section-description {
    color: #5f6368;
    margin-bottom: 24px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: #202124;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.form-text {
    font-size: 12px;
    color: #5f6368;
    margin-top: 4px;
}

.input-group {
    display: flex;
    gap: 8px;
}

.input-group .form-control {
    flex: 1;
}

.template-textarea {
    min-height: 200px;
    resize: vertical;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* 复选框样式 */
.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #dadce0;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #1976d2;
    border-color: #1976d2;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* 按钮样式 */
.btn-primary {
    background: #1976d2;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.btn-primary:hover {
    background: #1565c0;
}

.btn-secondary {
    background: #f1f3f4;
    color: #5f6368;
    border: 1px solid #dadce0;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: #e8eaed;
    color: #202124;
}

.btn-danger {
    background: #d93025;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.btn-danger:hover {
    background: #c5221f;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

/* 状态消息样式 */
.status-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin-top: 16px;
    font-size: 14px;
}

.status-message.success {
    background: #e8f5e8;
    color: #137333;
    border: 1px solid #c6e7c6;
}

.status-message.error {
    background: #fce8e6;
    color: #d93025;
    border: 1px solid #f9ab9d;
}

.status-message.info {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

/* API指南样式 */
.api-guide {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.guide-item {
    padding: 20px;
    border: 1px solid #dadce0;
    border-radius: 8px;
    background: #f8f9fa;
}

.guide-item h4 {
    margin-bottom: 12px;
    color: #202124;
}

.guide-item p {
    margin-bottom: 8px;
    font-size: 14px;
    color: #5f6368;
}

.guide-item a {
    color: #1976d2;
    text-decoration: none;
}

.guide-item a:hover {
    text-decoration: underline;
}

.guide-item code {
    background: #e8eaed;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    background: #1976d2;
    color: white;
    font-size: 12px;
    border-radius: 12px;
    margin-top: 8px;
}

/* 模板列表样式 */
.template-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.templates-list {
    display: grid;
    gap: 16px;
}

.template-item {
    padding: 20px;
    border: 1px solid #dadce0;
    border-radius: 8px;
    background: #f8f9fa;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.template-name {
    font-weight: 600;
    color: #202124;
}

.template-default {
    background: #34a853;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.template-actions-btn {
    display: flex;
    gap: 8px;
}

.template-preview {
    background: white;
    padding: 12px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    color: #5f6368;
    max-height: 100px;
    overflow: hidden;
    position: relative;
}

.template-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, white);
}

/* 历史记录样式 */
.history-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.history-list {
    display: grid;
    gap: 16px;
}

.history-item {
    padding: 20px;
    border: 1px solid #dadce0;
    border-radius: 8px;
    background: white;
    position: relative;
    transition: all 0.2s ease;
}

.history-item.selected {
    border-color: #1976d2;
    background: #f3f8ff;
}

.history-item-checkbox {
    position: absolute;
    top: 16px;
    left: 16px;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.history-item-checkbox input[type="checkbox"] {
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.history-item.has-checkbox {
    padding-left: 50px;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.history-title {
    font-weight: 600;
    color: #202124;
    margin-bottom: 4px;
}

.history-meta {
    font-size: 12px;
    color: #5f6368;
}

.history-actions-btn {
    display: flex;
    gap: 8px;
}

.history-summary {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    max-height: 150px;
    overflow: hidden;
    position: relative;
}

.history-summary.expanded {
    max-height: none;
}

.history-summary::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(transparent, #f8f9fa);
}

.history-summary.expanded::after {
    display: none;
}

/* 关于页面样式 */
.about-content {
    display: grid;
    gap: 32px;
}

.feature-list ul,
.shortcuts ul,
.support ul {
    list-style: none;
    padding-left: 0;
}

.feature-list li,
.shortcuts li,
.support li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.feature-list li:last-child,
.shortcuts li:last-child,
.support li:last-child {
    border-bottom: none;
}

.tech-info p {
    margin-bottom: 8px;
}

.shortcuts kbd {
    background: #f1f3f4;
    border: 1px solid #dadce0;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 12px;
    font-family: monospace;
}

/* 模态对话框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 24px;
    border-radius: 8px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
    margin-bottom: 16px;
    color: #202124;
}

.modal-content p {
    margin-bottom: 24px;
    color: #5f6368;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 0;
        box-shadow: none;
    }
    
    .header {
        padding: 16px 20px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .section {
        padding: 16px;
    }
    
    .tabs {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .tab-button {
        padding: 12px 16px;
        font-size: 13px;
    }
    
    .api-guide {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions button {
        width: 100%;
    }
    
    .input-group {
        flex-direction: column;
    }
}

/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}