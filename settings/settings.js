/**
 * 设置页面JavaScript逻辑
 * 负责设置页面的交互和数据管理
 */

(function() {
    'use strict';

    // 全局变量
    let currentEditingTemplate = null;
    let templates = [];
    let settings = {};

    // DOM元素
    const elements = {
        // 标签页
        tabButtons: document.querySelectorAll('.tab-button'),
        tabContents: document.querySelectorAll('.tab-content'),
        
        // API配置
        apiProvider: document.getElementById('api-provider'),
        apiKey: document.getElementById('api-key'),
        apiBaseUrl: document.getElementById('api-base-url'),
        toggleApiKey: document.getElementById('toggle-api-key'),
        saveApiConfig: document.getElementById('save-api-config'),
        testApiConnection: document.getElementById('test-api-connection'),
        apiStatus: document.getElementById('api-status'),
        
        // 模板管理
        addTemplate: document.getElementById('add-template'),
        resetTemplates: document.getElementById('reset-templates'),
        templatesList: document.getElementById('templates-list'),
        templateEditor: document.getElementById('template-editor'),
        editorTitle: document.getElementById('editor-title'),
        templateName: document.getElementById('template-name'),
        templateContent: document.getElementById('template-content'),
        saveTemplate: document.getElementById('save-template'),
        cancelEdit: document.getElementById('cancel-edit'),
        
        // 基础设置
        defaultProvider: document.getElementById('default-provider'),
        defaultTemplate: document.getElementById('default-template'),
        preferredLanguage: document.getElementById('preferred-language'),
        autoSummarize: document.getElementById('auto-summarize'),
        showHistory: document.getElementById('show-history'),
        maxHistory: document.getElementById('max-history'),
        saveSettings: document.getElementById('save-settings'),
        resetSettings: document.getElementById('reset-settings'),
        settingsStatus: document.getElementById('settings-status'),
        
        // 历史记录
        refreshHistory: document.getElementById('refresh-history'),
        exportHistory: document.getElementById('export-history'),
        selectAllHistory: document.getElementById('select-all-history'),
        deleteSelectedHistory: document.getElementById('delete-selected-history'),
        clearHistory: document.getElementById('clear-history'),
        historyList: document.getElementById('history-list'),
        historyStats: document.getElementById('history-stats'),
        
        // 数据管理
        exportData: document.getElementById('export-data'),
        importData: document.getElementById('import-data'),
        importFile: document.getElementById('import-file'),
        clearAllData: document.getElementById('clear-all-data'),
        
        // 确认对话框
        confirmDialog: document.getElementById('confirm-dialog'),
        confirmTitle: document.getElementById('confirm-title'),
        confirmMessage: document.getElementById('confirm-message'),
        confirmYes: document.getElementById('confirm-yes'),
        confirmNo: document.getElementById('confirm-no')
    };

    // 初始化
    document.addEventListener('DOMContentLoaded', init);

    async function init() {
        console.log('初始化设置页面');
        
        // 绑定事件监听器
        bindEventListeners();
        
        // 加载数据
        await loadAllData();
        
        // 初始化UI
        initializeUI();
        
        console.log('设置页面初始化完成');
    }

    /**
     * 绑定事件监听器
     */
    function bindEventListeners() {
        // 标签页切换
        elements.tabButtons.forEach(button => {
            button.addEventListener('click', () => switchTab(button.dataset.tab));
        });

        // API配置
        elements.toggleApiKey.addEventListener('click', toggleApiKeyVisibility);
        elements.saveApiConfig.addEventListener('click', saveApiConfig);
        elements.testApiConnection.addEventListener('click', testApiConnection);
        elements.apiProvider.addEventListener('change', updateApiBaseUrl);

        // 模板管理
        elements.addTemplate.addEventListener('click', () => editTemplate(null));
        elements.resetTemplates.addEventListener('click', resetTemplates);
        elements.saveTemplate.addEventListener('click', saveTemplate);
        elements.cancelEdit.addEventListener('click', cancelTemplateEdit);

        // 基础设置
        elements.saveSettings.addEventListener('click', saveSettings);
        elements.resetSettings.addEventListener('click', resetSettings);

        // 历史记录
        elements.refreshHistory.addEventListener('click', loadHistoryData);
        elements.exportHistory.addEventListener('click', exportHistory);
        elements.selectAllHistory.addEventListener('click', toggleSelectAllHistory);
        elements.deleteSelectedHistory.addEventListener('click', deleteSelectedHistory);
        elements.clearHistory.addEventListener('click', () => confirmAction('清空历史记录', '确定要清空所有历史记录吗？此操作不可撤销。', clearHistory));

        // 数据管理
        elements.exportData.addEventListener('click', exportAllData);
        elements.importData.addEventListener('click', () => elements.importFile.click());
        elements.importFile.addEventListener('change', importAllData);
        elements.clearAllData.addEventListener('click', () => confirmAction('清空所有数据', '确定要清空所有数据吗？包括API配置、模板、历史记录等。此操作不可撤销！', clearAllData));

        // 确认对话框
        elements.confirmNo.addEventListener('click', hideConfirmDialog);
    }

    /**
     * 切换标签页
     */
    function switchTab(tabId) {
        // 更新标签按钮状态
        elements.tabButtons.forEach(button => {
            button.classList.toggle('active', button.dataset.tab === tabId);
        });

        // 更新内容区域
        elements.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabId}-tab`);
        });

        // 特殊处理
        if (tabId === 'history') {
            loadHistoryData();
        }
    }

    /**
     * 加载所有数据
     */
    async function loadAllData() {
        try {
            await Promise.all([
                loadApiConfig(),
                loadTemplates(),
                loadSettings(),
                loadHistoryData()
            ]);
        } catch (error) {
            console.error('加载数据失败:', error);
            showStatus('error', '加载数据失败: ' + error.message);
        }
    }

    /**
     * 加载API配置
     */
    async function loadApiConfig() {
        const response = await sendMessage({ action: 'getApiConfig' });
        if (response.success && response.data) {
            const config = response.data;
            elements.apiProvider.value = config.provider || 'deepseek';
            elements.apiKey.value = config.apiKey || '';
            elements.apiBaseUrl.value = config.baseUrl || '';
            updateApiBaseUrl();
        }
    }

    /**
     * 加载模板数据
     */
    async function loadTemplates() {
        const response = await sendMessage({ action: 'getTemplates' });
        if (response.success) {
            templates = response.data || [];
            renderTemplatesList();
            updateDefaultTemplateOptions();
        }
    }

    /**
     * 加载设置数据
     */
    async function loadSettings() {
        const response = await sendMessage({ action: 'getSettings' });
        if (response.success) {
            settings = response.data || {};
            
            elements.defaultProvider.value = settings.defaultProvider || 'deepseek';
            elements.defaultTemplate.value = settings.defaultTemplate || 'basic';
            elements.preferredLanguage.value = settings.language || 'zh';
            elements.autoSummarize.checked = settings.autoSummarize || false;
            elements.showHistory.checked = settings.showHistory !== false;
            elements.maxHistory.value = settings.maxHistoryItems || 50;
        }
    }

    /**
     * 加载历史记录数据
     */
    async function loadHistoryData() {
        const response = await sendMessage({ action: 'getSummaryHistory', data: { limit: 50 } });
        if (response.success) {
            renderHistoryList(response.data || []);
        }
    }

    /**
     * 初始化UI
     */
    function initializeUI() {
        // 设置默认标签页
        switchTab('api');
        
        // 更新API基础URL
        updateApiBaseUrl();
    }

    /**
     * 切换API密钥可见性
     */
    function toggleApiKeyVisibility() {
        const isPassword = elements.apiKey.type === 'password';
        elements.apiKey.type = isPassword ? 'text' : 'password';
        elements.toggleApiKey.textContent = isPassword ? '隐藏' : '显示';
    }

    /**
     * 更新API基础URL
     */
    function updateApiBaseUrl() {
        const provider = elements.apiProvider.value;
        const defaultUrls = {
            deepseek: 'https://api.deepseek.com',
            openai: 'https://api.openai.com'
        };
        
        if (!elements.apiBaseUrl.value) {
            elements.apiBaseUrl.value = defaultUrls[provider] || '';
        }
    }

    /**
     * 保存API配置
     */
    async function saveApiConfig() {
        try {
            const config = {
                provider: elements.apiProvider.value,
                apiKey: elements.apiKey.value.trim(),
                baseUrl: elements.apiBaseUrl.value.trim()
            };

            if (!config.apiKey) {
                throw new Error('请输入API密钥');
            }

            const response = await sendMessage({ action: 'saveApiConfig', data: config });
            
            if (response.success) {
                showStatus('success', 'API配置保存成功', elements.apiStatus);
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            showStatus('error', error.message, elements.apiStatus);
        }
    }

    /**
     * 测试API连接
     */
    async function testApiConnection() {
        try {
            elements.testApiConnection.disabled = true;
            elements.testApiConnection.innerHTML = '<span class="loading"></span> 测试中...';
            
            const provider = elements.apiProvider.value;
            const response = await sendMessage({ action: 'testApiConnection', data: { provider } });
            
            if (response.success) {
                showStatus('success', 'API连接测试成功', elements.apiStatus);
            } else {
                throw new Error(response.error || '连接测试失败');
            }
        } catch (error) {
            showStatus('error', '连接测试失败: ' + error.message, elements.apiStatus);
        } finally {
            elements.testApiConnection.disabled = false;
            elements.testApiConnection.innerHTML = '测试连接';
        }
    }

    /**
     * 渲染模板列表
     */
    function renderTemplatesList() {
        elements.templatesList.innerHTML = '';
        
        templates.forEach(template => {
            const templateItem = document.createElement('div');
            templateItem.className = 'template-item';
            
            templateItem.innerHTML = `
                <div class="template-header">
                    <div>
                        <span class="template-name">${escapeHtml(template.name)}</span>
                        ${template.isDefault ? '<span class="template-default">默认</span>' : ''}
                    </div>
                    <div class="template-actions-btn">
                        <button type="button" class="btn-secondary" onclick="editTemplate('${template.id}')">编辑</button>
                        ${!template.isDefault ? `<button type="button" class="btn-danger" onclick="deleteTemplate('${template.id}')">删除</button>` : ''}
                    </div>
                </div>
                <div class="template-preview">${escapeHtml(template.template.substring(0, 200))}${template.template.length > 200 ? '...' : ''}</div>
            `;
            
            elements.templatesList.appendChild(templateItem);
        });
    }

    /**
     * 编辑模板
     */
    window.editTemplate = function(templateId) {
        currentEditingTemplate = templateId;
        
        if (templateId) {
            const template = templates.find(t => t.id === templateId);
            if (template) {
                elements.editorTitle.textContent = '编辑模板';
                elements.templateName.value = template.name;
                elements.templateContent.value = template.template;
            }
        } else {
            elements.editorTitle.textContent = '添加新模板';
            elements.templateName.value = '';
            elements.templateContent.value = '';
        }
        
        elements.templateEditor.style.display = 'block';
        elements.templateName.focus();
    };

    /**
     * 删除模板
     */
    window.deleteTemplate = function(templateId) {
        const template = templates.find(t => t.id === templateId);
        if (template) {
            confirmAction('删除模板', `确定要删除模板"${template.name}"吗？`, async () => {
                const response = await sendMessage({ action: 'deleteTemplate', data: { templateId } });
                if (response.success) {
                    await loadTemplates();
                    showStatus('success', '模板删除成功');
                } else {
                    showStatus('error', response.message || '删除失败');
                }
            });
        }
    };

    /**
     * 保存模板
     */
    async function saveTemplate() {
        try {
            const name = elements.templateName.value.trim();
            const content = elements.templateContent.value.trim();
            
            if (!name) {
                throw new Error('请输入模板名称');
            }
            
            if (!content) {
                throw new Error('请输入模板内容');
            }
            
            if (!content.includes('{content}')) {
                throw new Error('模板必须包含 {content} 占位符');
            }
            
            const templateData = {
                id: currentEditingTemplate,
                name: name,
                template: content
            };
            
            const response = await sendMessage({ action: 'saveTemplate', data: templateData });
            
            if (response.success) {
                await loadTemplates();
                cancelTemplateEdit();
                showStatus('success', '模板保存成功');
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            showStatus('error', error.message);
        }
    }

    /**
     * 取消模板编辑
     */
    function cancelTemplateEdit() {
        elements.templateEditor.style.display = 'none';
        currentEditingTemplate = null;
    }

    /**
     * 重置模板
     */
    async function resetTemplates() {
        confirmAction('重置模板', '确定要重置为默认模板吗？这将删除所有自定义模板。', async () => {
            try {
                // 获取默认模板
                const defaultTemplates = [
                    {
                        id: 'basic',
                        name: '基础总结',
                        template: `请对以下YouTube视频内容进行总结：

🎯 核心主题：[视频的主要话题和中心观点]

📝 关键要点：
1. [要点1]
2. [要点2] 
3. [要点3]

💡 主要论点：
• [论点1及其支撑证据]
• [论点2及其支撑证据]

🔍 深度分析：[对内容的详细解读和分析]

✨ 学习价值：[视频的教育意义和实用价值]

内容：{content}`,
                        isDefault: true
                    },
                    {
                        id: 'concise',
                        name: '简洁总结',
                        template: `请用简洁的方式总结以下YouTube视频内容，重点突出核心观点和关键信息：

内容：{content}`,
                        isDefault: true
                    },
                    {
                        id: 'detailed',
                        name: '详细分析',
                        template: `请对以下YouTube视频内容进行详细分析：

📋 内容概述：[视频整体内容的简要概述]

🎯 核心观点：
1. [核心观点1 - 详细说明]
2. [核心观点2 - 详细说明]
3. [核心观点3 - 详细说明]

📊 论证结构：
• 前提假设：[视频基于的前提或假设]
• 论证过程：[如何展开论证]
• 结论要点：[得出的主要结论]

🔗 相关联系：[与其他知识点或现实情况的联系]

💭 个人思考：[值得深入思考的问题或启发]

内容：{content}`,
                        isDefault: true
                    }
                ];
                
                const response = await sendMessage({ action: 'saveTemplate', data: { templates: defaultTemplates } });
                
                if (response.success) {
                    await loadTemplates();
                    showStatus('success', '模板重置成功');
                } else {
                    throw new Error(response.message || '重置失败');
                }
            } catch (error) {
                showStatus('error', '重置失败: ' + error.message);
            }
        });
    }

    /**
     * 更新默认模板选项
     */
    function updateDefaultTemplateOptions() {
        elements.defaultTemplate.innerHTML = '';
        templates.forEach(template => {
            const option = document.createElement('option');
            option.value = template.id;
            option.textContent = template.name;
            elements.defaultTemplate.appendChild(option);
        });
    }

    /**
     * 保存设置
     */
    async function saveSettings() {
        try {
            const settingsData = {
                defaultProvider: elements.defaultProvider.value,
                defaultTemplate: elements.defaultTemplate.value,
                language: elements.preferredLanguage.value,
                autoSummarize: elements.autoSummarize.checked,
                showHistory: elements.showHistory.checked,
                maxHistoryItems: parseInt(elements.maxHistory.value)
            };
            
            const response = await sendMessage({ action: 'saveSettings', data: settingsData });
            
            if (response.success) {
                settings = settingsData;
                showStatus('success', '设置保存成功', elements.settingsStatus);
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            showStatus('error', error.message, elements.settingsStatus);
        }
    }

    /**
     * 重置设置
     */
    function resetSettings() {
        confirmAction('重置设置', '确定要重置为默认设置吗？', () => {
            elements.defaultProvider.value = 'deepseek';
            elements.defaultTemplate.value = 'basic';
            elements.preferredLanguage.value = 'zh';
            elements.autoSummarize.checked = false;
            elements.showHistory.checked = true;
            elements.maxHistory.value = 50;
            
            saveSettings();
        });
    }

    /**
     * 渲染历史记录列表
     */
    function renderHistoryList(history) {
        elements.historyList.innerHTML = '';

        // 更新统计信息
        updateHistoryStats(history);

        if (history.length === 0) {
            elements.historyList.innerHTML = '<p style="text-align: center; color: #5f6368; padding: 40px;">暂无历史记录</p>';
            elements.deleteSelectedHistory.disabled = true;
            return;
        }

        history.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item has-checkbox';
            historyItem.dataset.itemId = item.id;

            const date = new Date(item.timestamp).toLocaleString('zh-CN');

            historyItem.innerHTML = `
                <div class="history-item-checkbox">
                    <input type="checkbox" id="checkbox-${item.id}" onchange="updateSelectedCount()">
                </div>
                <div class="history-header">
                    <div>
                        <div class="history-title">${escapeHtml(item.videoTitle || '未知标题')}</div>
                        <div class="history-meta">
                            ${date} | ${item.provider || '未知'} | 视频ID: ${item.videoId || '未知'}
                        </div>
                    </div>
                    <div class="history-actions-btn">
                        <button type="button" class="btn-secondary" onclick="copyHistoryText('${item.id}')">📋 复制</button>
                        <button type="button" class="btn-danger" onclick="deleteHistoryItem('${item.id}')">🗑️ 删除</button>
                    </div>
                </div>
                <div class="history-summary" id="summary-${item.id}">
                    ${escapeHtml(item.summary || '无总结内容')}
                </div>
                <button type="button" class="btn-secondary" onclick="toggleHistorySummary('${item.id}')" style="margin-top: 8px;">
                    📖 展开/收起
                </button>
            `;

            elements.historyList.appendChild(historyItem);
        });

        // 重置选择状态
        updateSelectedCount();
    }

    /**
     * 更新历史记录统计信息
     */
    function updateHistoryStats(history) {
        if (!elements.historyStats) return;

        const totalCount = history.length;
        const providers = {};
        let totalSize = 0;

        history.forEach(item => {
            const provider = item.provider || '未知';
            providers[provider] = (providers[provider] || 0) + 1;
            totalSize += (item.summary || '').length;
        });

        const providerStats = Object.entries(providers)
            .map(([provider, count]) => `${provider}: ${count}`)
            .join(', ');

        const avgSize = totalCount > 0 ? Math.round(totalSize / totalCount) : 0;

        elements.historyStats.innerHTML = `
            📊 总计: ${totalCount} 条记录 |
            🤖 提供商分布: ${providerStats} |
            📏 平均长度: ${avgSize} 字符 |
            💾 总大小: ${(totalSize / 1024).toFixed(1)} KB
        `;
    }

    /**
     * 切换历史记录总结展示
     */
    window.toggleHistorySummary = function(itemId) {
        const summaryElement = document.getElementById(`summary-${itemId}`);
        if (summaryElement) {
            summaryElement.classList.toggle('expanded');
        }
    };

    /**
     * 复制历史记录文本
     */
    window.copyHistoryText = function(itemId) {
        const summaryElement = document.getElementById(`summary-${itemId}`);
        if (summaryElement) {
            navigator.clipboard.writeText(summaryElement.textContent).then(() => {
                showStatus('success', '文本已复制到剪贴板');
            }).catch(() => {
                showStatus('error', '复制失败');
            });
        }
    };

    /**
     * 删除历史记录项
     */
    window.deleteHistoryItem = function(itemId) {
        confirmAction('删除历史记录', '确定要删除这条历史记录吗？', async () => {
            const response = await sendMessage({ action: 'deleteSummaryHistory', data: { summaryId: itemId } });
            if (response.success) {
                await loadHistoryData();
                showStatus('success', '历史记录删除成功');
            } else {
                showStatus('error', response.message || '删除失败');
            }
        });
    };

    /**
     * 导出历史记录
     */
    async function exportHistory() {
        try {
            const response = await sendMessage({ action: 'getSummaryHistory', data: { limit: 1000 } });
            if (response.success) {
                const data = {
                    exportTime: new Date().toISOString(),
                    history: response.data
                };
                
                downloadJson(data, `youtube-summarizer-history-${new Date().toISOString().split('T')[0]}.json`);
                showStatus('success', '历史记录导出成功');
            } else {
                throw new Error(response.message || '导出失败');
            }
        } catch (error) {
            showStatus('error', '导出失败: ' + error.message);
        }
    }

    /**
     * 清空历史记录
     */
    async function clearHistory() {
        try {
            const response = await sendMessage({ action: 'clearAllSummaryHistory' });
            if (response.success) {
                await loadHistoryData();
                showStatus('success', '历史记录清空成功');
            } else {
                throw new Error(response.message || '清空失败');
            }
        } catch (error) {
            showStatus('error', '清空失败: ' + error.message);
        }
    }

    /**
     * 切换全选状态
     */
    function toggleSelectAllHistory() {
        const checkboxes = elements.historyList.querySelectorAll('input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
            const historyItem = checkbox.closest('.history-item');
            if (historyItem) {
                historyItem.classList.toggle('selected', checkbox.checked);
            }
        });

        updateSelectedCount();

        // 更新按钮文本
        elements.selectAllHistory.textContent = allChecked ? '☑️ 全选' : '☐ 取消全选';
    }

    /**
     * 更新选中数量和按钮状态
     */
    window.updateSelectedCount = function() {
        const checkboxes = elements.historyList.querySelectorAll('input[type="checkbox"]');
        const selectedCheckboxes = elements.historyList.querySelectorAll('input[type="checkbox"]:checked');

        // 更新选中项的样式
        checkboxes.forEach(checkbox => {
            const historyItem = checkbox.closest('.history-item');
            if (historyItem) {
                historyItem.classList.toggle('selected', checkbox.checked);
            }
        });

        // 更新按钮状态
        const selectedCount = selectedCheckboxes.length;
        const totalCount = checkboxes.length;

        elements.deleteSelectedHistory.disabled = selectedCount === 0;
        elements.deleteSelectedHistory.textContent = selectedCount > 0
            ? `🗑️ 删除选中 (${selectedCount})`
            : '🗑️ 删除选中';

        // 更新全选按钮状态
        if (selectedCount === 0) {
            elements.selectAllHistory.textContent = '☑️ 全选';
        } else if (selectedCount === totalCount) {
            elements.selectAllHistory.textContent = '☐ 取消全选';
        } else {
            elements.selectAllHistory.textContent = `☑️ 全选 (${selectedCount}/${totalCount})`;
        }
    };

    /**
     * 删除选中的历史记录
     */
    async function deleteSelectedHistory() {
        const selectedCheckboxes = elements.historyList.querySelectorAll('input[type="checkbox"]:checked');
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.id.replace('checkbox-', ''));

        if (selectedIds.length === 0) {
            showStatus('warning', '请先选择要删除的记录');
            return;
        }

        confirmAction(
            '批量删除历史记录',
            `确定要删除选中的 ${selectedIds.length} 条历史记录吗？此操作不可撤销。`,
            async () => {
                try {
                    const response = await sendMessage({
                        action: 'deleteSummaryHistoryBatch',
                        data: { summaryIds: selectedIds }
                    });

                    if (response.success) {
                        await loadHistoryData();
                        showStatus('success', response.message || '批量删除成功');
                    } else {
                        throw new Error(response.message || '批量删除失败');
                    }
                } catch (error) {
                    showStatus('error', '批量删除失败: ' + error.message);
                }
            }
        );
    }

    /**
     * 导出所有数据
     */
    async function exportAllData() {
        try {
            const response = await sendMessage({ action: 'exportData' });
            if (response.success) {
                downloadJson(response.data, `youtube-summarizer-backup-${new Date().toISOString().split('T')[0]}.json`);
                showStatus('success', '数据导出成功');
            } else {
                throw new Error(response.message || '导出失败');
            }
        } catch (error) {
            showStatus('error', '导出失败: ' + error.message);
        }
    }

    /**
     * 导入所有数据
     */
    async function importAllData(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        try {
            const text = await file.text();
            const data = JSON.parse(text);
            
            const response = await sendMessage({ action: 'importData', data: { importData: data } });
            if (response.success) {
                await loadAllData();
                showStatus('success', '数据导入成功');
            } else {
                throw new Error(response.message || '导入失败');
            }
        } catch (error) {
            showStatus('error', '导入失败: ' + error.message);
        } finally {
            elements.importFile.value = '';
        }
    }

    /**
     * 清空所有数据
     */
    async function clearAllData() {
        try {
            // 这里需要调用后台脚本的清空数据方法
            const response = await sendMessage({ action: 'clearAllData' });
            if (response.success) {
                await loadAllData();
                showStatus('success', '所有数据清空成功');
            } else {
                throw new Error(response.message || '清空失败');
            }
        } catch (error) {
            showStatus('error', '清空失败: ' + error.message);
        }
    }

    /**
     * 显示确认对话框
     */
    function confirmAction(title, message, callback) {
        elements.confirmTitle.textContent = title;
        elements.confirmMessage.textContent = message;
        elements.confirmDialog.style.display = 'flex';
        
        elements.confirmYes.onclick = () => {
            hideConfirmDialog();
            callback();
        };
    }

    /**
     * 隐藏确认对话框
     */
    function hideConfirmDialog() {
        elements.confirmDialog.style.display = 'none';
        elements.confirmYes.onclick = null;
    }

    /**
     * 显示状态消息
     */
    function showStatus(type, message, element = null) {
        const statusElement = element || document.createElement('div');
        
        if (!element) {
            statusElement.className = 'status-message';
            statusElement.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 1001; max-width: 400px;';
            document.body.appendChild(statusElement);
            
            setTimeout(() => {
                if (statusElement.parentNode) {
                    statusElement.parentNode.removeChild(statusElement);
                }
            }, 5000);
        }
        
        statusElement.className = `status-message ${type}`;
        statusElement.textContent = message;
        statusElement.style.display = 'block';
        
        if (element) {
            setTimeout(() => {
                statusElement.style.display = 'none';
            }, 5000);
        }
    }

    /**
     * 发送消息到后台脚本
     */
    function sendMessage(message) {
        return new Promise((resolve) => {
            browser.runtime.sendMessage(message, resolve);
        });
    }

    /**
     * 下载JSON文件
     */
    function downloadJson(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * HTML转义
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

})();