<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube总结器 - 设置</title>
    <link rel="stylesheet" href="settings.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="#065fd4">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <h1>YouTube总结器</h1>
                </div>
                <div class="version">v1.0.1</div>
            </div>
        </header>

        <!-- 导航标签 -->
        <nav class="tabs">
            <button class="tab-button active" data-tab="api">API配置</button>
            <button class="tab-button" data-tab="templates">提示模板</button>
            <button class="tab-button" data-tab="settings">基础设置</button>
            <button class="tab-button" data-tab="history">历史记录</button>
            <button class="tab-button" data-tab="about">关于</button>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- API配置标签页 -->
            <div id="api-tab" class="tab-content active">
                <div class="section">
                    <h2>AI API配置</h2>
                    <p class="section-description">配置您的AI服务提供商API密钥</p>

                    <div class="form-group">
                        <label for="api-provider">API提供商</label>
                        <select id="api-provider" class="form-control">
                            <option value="deepseek">DeepSeek (推荐)</option>
                            <option value="openai">OpenAI</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="api-key">API密钥</label>
                        <div class="input-group">
                            <input type="password" id="api-key" class="form-control" placeholder="请输入您的API密钥">
                            <button type="button" id="toggle-api-key" class="btn-secondary">显示</button>
                        </div>
                        <small class="form-text">您的API密钥将被安全加密存储在本地</small>
                    </div>

                    <div class="form-group">
                        <label for="api-base-url">API基础URL (可选)</label>
                        <input type="url" id="api-base-url" class="form-control" placeholder="留空使用默认URL">
                        <small class="form-text">自定义API端点，通常保持默认即可</small>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="save-api-config" class="btn-primary">保存配置</button>
                        <button type="button" id="test-api-connection" class="btn-secondary">测试连接</button>
                    </div>

                    <div id="api-status" class="status-message" style="display: none;"></div>
                </div>

                <!-- API使用说明 -->
                <div class="section">
                    <h3>API获取指南</h3>
                    <div class="api-guide">
                        <div class="guide-item">
                            <h4>DeepSeek (推荐)</h4>
                            <p>1. 访问 <a href="https://platform.deepseek.com" target="_blank">DeepSeek平台</a></p>
                            <p>2. 注册账号并登录</p>
                            <p>3. 在API密钥页面创建新的密钥</p>
                            <p>4. 复制密钥并粘贴到上方输入框</p>
                            <span class="badge">低成本</span>
                        </div>
                        
                        <div class="guide-item">
                            <h4>OpenAI</h4>
                            <p>1. 访问 <a href="https://platform.openai.com" target="_blank">OpenAI平台</a></p>
                            <p>2. 登录并进入API密钥页面</p>
                            <p>3. 创建新的API密钥</p>
                            <p>4. 复制密钥并配置</p>
                            <span class="badge">高质量</span>
                        </div>
                        
                        
                    </div>
                </div>
            </div>

            <!-- 提示模板标签页 -->
            <div id="templates-tab" class="tab-content">
                <div class="section">
                    <h2>提示模板管理</h2>
                    <p class="section-description">自定义AI总结的提示模板</p>

                    <div class="template-actions">
                        <button type="button" id="add-template" class="btn-primary">添加新模板</button>
                        <button type="button" id="reset-templates" class="btn-secondary">重置为默认</button>
                    </div>

                    <div id="templates-list" class="templates-list">
                        <!-- 模板列表将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 模板编辑器 -->
                <div id="template-editor" class="section" style="display: none;">
                    <h3 id="editor-title">编辑模板</h3>
                    
                    <div class="form-group">
                        <label for="template-name">模板名称</label>
                        <input type="text" id="template-name" class="form-control" placeholder="输入模板名称">
                    </div>

                    <div class="form-group">
                        <label for="template-content">模板内容</label>
                        <textarea id="template-content" class="form-control template-textarea" 
                                  placeholder="输入提示模板，使用 {content} 作为视频内容的占位符"></textarea>
                        <small class="form-text">使用 {content} 作为视频内容的占位符</small>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="save-template" class="btn-primary">保存模板</button>
                        <button type="button" id="cancel-edit" class="btn-secondary">取消</button>
                    </div>
                </div>
            </div>

            <!-- 基础设置标签页 -->
            <div id="settings-tab" class="tab-content">
                <div class="section">
                    <h2>基础设置</h2>
                    <p class="section-description">配置扩展的基本行为</p>

                    <div class="form-group">
                        <label for="default-provider">默认AI提供商</label>
                        <select id="default-provider" class="form-control">
                            <option value="deepseek">DeepSeek</option>
                            <option value="openai">OpenAI</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="default-template">默认提示模板</label>
                        <select id="default-template" class="form-control">
                            <!-- 选项将通过JavaScript动态生成 -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="preferred-language">首选字幕语言</label>
                        <select id="preferred-language" class="form-control">
                            <option value="zh">中文</option>
                            <option value="zh-CN">中文(简体)</option>
                            <option value="zh-TW">中文(繁体)</option>
                            <option value="en">英语</option>
                            <option value="ja">日语</option>
                            <option value="ko">韩语</option>
                            <option value="auto">自动检测</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="auto-summarize">
                                <span class="checkmark"></span>
                                自动生成总结 (页面加载时)
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="show-history">
                                <span class="checkmark"></span>
                                显示历史记录
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="max-history">最大历史记录数量</label>
                        <input type="number" id="max-history" class="form-control" min="10" max="200" value="50">
                    </div>

                    <div class="form-actions">
                        <button type="button" id="save-settings" class="btn-primary">保存设置</button>
                        <button type="button" id="reset-settings" class="btn-secondary">重置设置</button>
                    </div>

                    <div id="settings-status" class="status-message" style="display: none;"></div>
                </div>
            </div>

            <!-- 历史记录标签页 -->
            <div id="history-tab" class="tab-content">
                <div class="section">
                    <h2>总结历史</h2>
                    <p class="section-description">查看和管理您的总结历史记录</p>

                    <div class="history-actions">
                        <button type="button" id="refresh-history" class="btn-secondary">🔄 刷新</button>
                        <button type="button" id="export-history" class="btn-secondary">📥 导出历史</button>
                        <button type="button" id="select-all-history" class="btn-secondary">☑️ 全选</button>
                        <button type="button" id="delete-selected-history" class="btn-danger" disabled>🗑️ 删除选中</button>
                        <button type="button" id="clear-history" class="btn-danger">🗑️ 清空所有</button>
                    </div>

                    <div class="history-stats" id="history-stats" style="margin: 16px 0; padding: 12px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #5f6368;">
                        <!-- 统计信息将通过JavaScript动态生成 -->
                    </div>

                    <div id="history-list" class="history-list">
                        <!-- 历史记录将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 关于标签页 -->
            <div id="about-tab" class="tab-content">
                <div class="section">
                    <h2>关于YouTube总结器</h2>
                    
                    <div class="about-content">
                        <div class="feature-list">
                            <h3>主要功能</h3>
                            <ul>
                                <li>✅ 自动提取YouTube视频字幕</li>
                                <li>✅ 支持多种AI模型生成总结</li>
                                <li>✅ 可自定义提示模板</li>
                                <li>✅ 总结历史记录管理</li>
                                <li>✅ 多语言字幕支持</li>
                                <li>✅ 安全的本地数据存储</li>
                            </ul>
                        </div>

                        <div class="tech-info">
                            <h3>技术信息</h3>
                            <p><strong>版本:</strong> 1.0.4</p>
                            <p><strong>支持的AI模型:</strong> DeepSeek, OpenAI</p>
                            <p><strong>支持的浏览器:</strong> Firefox</p>
                            <p><strong>开发语言:</strong> JavaScript, HTML, CSS</p>
                        </div>

                        <div class="data-management">
                            <h3>数据管理</h3>
                            <div class="form-actions">
                                <button type="button" id="export-data" class="btn-secondary">导出所有数据</button>
                                <button type="button" id="import-data" class="btn-secondary">导入数据</button>
                                <button type="button" id="clear-all-data" class="btn-danger">清空所有数据</button>
                            </div>
                            <input type="file" id="import-file" accept=".json" style="display: none;">
                        </div>

                        <div class="shortcuts">
                            <h3>快捷键</h3>
                            <ul>
                                <li><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>S</kbd> - 生成总结</li>
                                <li><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>C</kbd> - 清除缓存</li>
                            </ul>
                        </div>

                        <div class="support">
                            <h3>支持与反馈</h3>
                            <p>如果您遇到问题或有建议，请通过以下方式联系我们：</p>
                            <ul>
                                <li>📧 邮件支持</li>
                                <li>🐛 问题反馈</li>
                                <li>💡 功能建议</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 确认对话框 -->
    <div id="confirm-dialog" class="modal" style="display: none;">
        <div class="modal-content">
            <h3 id="confirm-title">确认操作</h3>
            <p id="confirm-message">您确定要执行此操作吗？</p>
            <div class="modal-actions">
                <button type="button" id="confirm-yes" class="btn-danger">确认</button>
                <button type="button" id="confirm-no" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script src="settings.js"></script>
</body>
</html>