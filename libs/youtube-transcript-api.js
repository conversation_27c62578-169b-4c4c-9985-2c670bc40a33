/**
 * YouTube字幕提取API
 * 简化版的JavaScript实现，用于提取YouTube视频字幕
 */

class YoutubeTranscript {
  constructor() {
    this.baseUrl = 'https://www.youtube.com';
  }

  /**
   * 从YouTube视频URL或ID提取字幕
   * @param {string} videoIdOrUrl - 视频ID或完整URL
   * @param {Object} options - 选项配置
   * @returns {Promise<Array>} 字幕数组
   */
  static async fetchTranscript(videoIdOrUrl, options = {}) {
    const instance = new YoutubeTranscript();
    return await instance.getTranscript(videoIdOrUrl, options);
  }

  /**
   * 获取视频字幕
   * @param {string} videoIdOrUrl - 视频ID或URL
   * @param {Object} options - 配置选项
   * @returns {Promise<Array>} 字幕数据
   */
  async getTranscript(videoIdOrUrl, options = {}) {
    try {
      const videoId = this.extractVideoId(videoIdOrUrl);
      if (!videoId) {
        throw new Error('无效的YouTube视频ID或URL');
      }

      // 获取视频页面HTML
      const videoPageHtml = await this.fetchVideoPage(videoId);
      
      // 提取字幕配置信息
      const captionsData = this.extractCaptionsData(videoPageHtml);
      
      if (!captionsData || captionsData.length === 0) {
        throw new Error('该视频没有可用的字幕');
      }

      // 选择合适的字幕轨道
      const selectedCaption = this.selectCaptionTrack(captionsData, options.lang);
      
      // 获取字幕内容
      const transcript = await this.fetchCaptionContent(selectedCaption.baseUrl);
      
      return this.parseTranscript(transcript);

    } catch (error) {
      console.error('获取字幕失败:', error);
      throw error;
    }
  }

  /**
   * 从URL中提取视频ID
   * @param {string} urlOrId - URL或视频ID
   * @returns {string|null} 视频ID
   */
  extractVideoId(urlOrId) {
    // 如果已经是视频ID格式
    if (/^[a-zA-Z0-9_-]{11}$/.test(urlOrId)) {
      return urlOrId;
    }

    // 从各种YouTube URL格式中提取ID
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
      /youtube\.com\/v\/([a-zA-Z0-9_-]{11})/,
      /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/
    ];

    for (const pattern of patterns) {
      const match = urlOrId.match(pattern);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * 获取视频页面HTML
   * @param {string} videoId - 视频ID
   * @returns {Promise<string>} 页面HTML
   */
  async fetchVideoPage(videoId) {
    const url = `${this.baseUrl}/watch?v=${videoId}`;
    
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.text();
    } catch (error) {
      throw new Error(`获取视频页面失败: ${error.message}`);
    }
  }

  /**
   * 从页面HTML中提取字幕配置数据
   * @param {string} html - 页面HTML
   * @returns {Array} 字幕配置数组
   */
  extractCaptionsData(html) {
    try {
      // 查找包含字幕信息的JSON数据
      const patterns = [
        /"captions":(\{.*?\}),/,
        /"playerCaptionsTracklistRenderer":(\{.*?\})/,
        /ytInitialPlayerResponse\s*=\s*(\{.*?\});/
      ];

      let captionsData = null;

      for (const pattern of patterns) {
        const match = html.match(pattern);
        if (match) {
          try {
            const data = JSON.parse(match[1]);
            captionsData = this.findCaptionsInData(data);
            if (captionsData) break;
          } catch (e) {
            continue;
          }
        }
      }

      if (!captionsData) {
        // 尝试更简单的方法：查找字幕URL模式
        return this.extractCaptionUrlsFromHtml(html);
      }

      return captionsData;
    } catch (error) {
      console.error('提取字幕数据失败:', error);
      return [];
    }
  }

  /**
   * 在数据对象中查找字幕信息
   * @param {Object} data - 数据对象
   * @returns {Array|null} 字幕数组
   */
  findCaptionsInData(data) {
    // 递归查找字幕数据
    const findCaptions = (obj) => {
      if (!obj || typeof obj !== 'object') return null;

      // 查找常见的字幕字段
      if (obj.captionTracks && Array.isArray(obj.captionTracks)) {
        return obj.captionTracks;
      }

      if (obj.playerCaptionsTracklistRenderer && obj.playerCaptionsTracklistRenderer.captionTracks) {
        return obj.playerCaptionsTracklistRenderer.captionTracks;
      }

      // 递归查找
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const result = findCaptions(obj[key]);
          if (result) return result;
        }
      }

      return null;
    };

    return findCaptions(data);
  }

  /**
   * 从HTML中直接提取字幕URL
   * @param {string} html - HTML内容
   * @returns {Array} 字幕URL数组
   */
  extractCaptionUrlsFromHtml(html) {
    const urls = [];
    const urlPattern = /https:\/\/www\.youtube\.com\/api\/timedtext[^"]+/g;
    const matches = html.match(urlPattern);

    if (matches) {
      matches.forEach(url => {
        // 解码URL
        const decodedUrl = url.replace(/\\u0026/g, '&').replace(/\\u003d/g, '=');
        urls.push({
          baseUrl: decodedUrl,
          languageCode: this.extractLangFromUrl(decodedUrl) || 'auto',
          name: { simpleText: 'Auto-generated' }
        });
      });
    }

    return urls;
  }

  /**
   * 从URL中提取语言代码
   * @param {string} url - 字幕URL
   * @returns {string|null} 语言代码
   */
  extractLangFromUrl(url) {
    const match = url.match(/[?&]lang=([^&]+)/);
    return match ? match[1] : null;
  }

  /**
   * 选择合适的字幕轨道
   * @param {Array} captionsData - 字幕数据数组
   * @param {string} preferredLang - 首选语言
   * @returns {Object} 选中的字幕轨道
   */
  selectCaptionTrack(captionsData, preferredLang = 'zh') {
    if (!captionsData || captionsData.length === 0) {
      throw new Error('没有可用的字幕轨道');
    }

    // 优先选择指定语言
    if (preferredLang) {
      const langTrack = captionsData.find(track => 
        track.languageCode && track.languageCode.startsWith(preferredLang)
      );
      if (langTrack) return langTrack;
    }

    // 选择中文字幕
    const chineseTrack = captionsData.find(track => 
      track.languageCode && (
        track.languageCode.startsWith('zh') ||
        track.languageCode.startsWith('cn')
      )
    );
    if (chineseTrack) return chineseTrack;

    // 选择英文字幕
    const englishTrack = captionsData.find(track => 
      track.languageCode && track.languageCode.startsWith('en')
    );
    if (englishTrack) return englishTrack;

    // 选择自动生成的字幕
    const autoTrack = captionsData.find(track => 
      track.kind === 'asr' || 
      (track.name && track.name.simpleText && track.name.simpleText.includes('auto'))
    );
    if (autoTrack) return autoTrack;

    // 返回第一个可用的字幕
    return captionsData[0];
  }

  /**
   * 获取字幕内容
   * @param {string} captionUrl - 字幕URL
   * @returns {Promise<string>} 字幕XML内容
   */
  async fetchCaptionContent(captionUrl) {
    try {
      // 确保URL格式正确
      let url = captionUrl;
      if (!url.includes('fmt=')) {
        url += url.includes('?') ? '&fmt=srv3' : '?fmt=srv3';
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.text();
    } catch (error) {
      throw new Error(`获取字幕内容失败: ${error.message}`);
    }
  }

  /**
   * 解析字幕XML内容
   * @param {string} xmlContent - XML内容
   * @returns {Array} 解析后的字幕数组
   */
  parseTranscript(xmlContent) {
    try {
      const transcript = [];
      
      // 使用正则表达式解析XML（简化版本）
      const textPattern = /<text[^>]*start="([^"]*)"[^>]*dur="([^"]*)"[^>]*>([^<]*)<\/text>/g;
      let match;

      while ((match = textPattern.exec(xmlContent)) !== null) {
        const start = parseFloat(match[1]);
        const duration = parseFloat(match[2]);
        let text = match[3];

        // 解码HTML实体
        text = this.decodeHtmlEntities(text);
        
        // 清理文本
        text = text.trim();
        if (text) {
          transcript.push({
            text: text,
            start: start,
            duration: duration,
            end: start + duration
          });
        }
      }

      if (transcript.length === 0) {
        throw new Error('无法解析字幕内容');
      }

      return transcript;
    } catch (error) {
      throw new Error(`解析字幕失败: ${error.message}`);
    }
  }

  /**
   * 解码HTML实体
   * @param {string} text - 包含HTML实体的文本
   * @returns {string} 解码后的文本
   */
  decodeHtmlEntities(text) {
    const entities = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'",
      '&nbsp;': ' '
    };

    return text.replace(/&[#\w]+;/g, (entity) => {
      return entities[entity] || entity;
    });
  }

  /**
   * 获取视频的可用字幕语言列表
   * @param {string} videoIdOrUrl - 视频ID或URL
   * @returns {Promise<Array>} 可用语言列表
   */
  static async getAvailableLanguages(videoIdOrUrl) {
    try {
      const instance = new YoutubeTranscript();
      const videoId = instance.extractVideoId(videoIdOrUrl);
      const videoPageHtml = await instance.fetchVideoPage(videoId);
      const captionsData = instance.extractCaptionsData(videoPageHtml);

      return captionsData.map(track => ({
        code: track.languageCode,
        name: track.name ? track.name.simpleText : track.languageCode,
        auto: track.kind === 'asr'
      }));
    } catch (error) {
      console.error('获取可用语言失败:', error);
      return [];
    }
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = YoutubeTranscript;
} else if (typeof window !== 'undefined') {
  window.YoutubeTranscript = YoutubeTranscript;
}