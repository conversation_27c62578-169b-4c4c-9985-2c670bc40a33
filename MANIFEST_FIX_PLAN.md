# Manifest.json 配置修复计划

## 问题分析

当前 `manifest.json` 中存在以下配置不一致问题：

### 1. 不存在的文件引用
- `web_accessible_resources` 中引用了不存在的文件：
  - `popup/summary-modal.html` ❌
  - `popup/summary-modal.css` ❌

### 2. Browser Action 配置错误
- `browser_action.default_popup` 指向不存在的文件
- 实际实现使用内容脚本注入，不需要popup

### 3. 图标文件配置
- 需要确认图标文件是否完整
- 当前引用：icon-16.png, icon-48.png, icon-128.png

## 修复方案

### 修复后的 manifest.json 配置

```json
{
  "manifest_version": 2,
  "name": "YouTube视频总结器",
  "version": "1.0.0",
  "description": "自动提取YouTube视频字幕并生成AI总结",
  
  "permissions": [
    "activeTab",
    "storage",
    "https://www.youtube.com/*",
    "https://api.deepseek.com/*",
    "https://api.openai.com/*",
    "http://localhost:*"
  ],
  
  "background": {
    "scripts": [
      "background/storage-manager.js",
      "background/api-handler.js", 
      "background/background.js"
    ],
    "persistent": false
  },
  
  "content_scripts": [
    {
      "matches": ["https://www.youtube.com/watch*"],
      "js": [
        "libs/youtube-transcript-api.js",
        "content/transcript-extractor.js",
        "content/youtube-injector.js",
        "content/content.js"
      ],
      "css": ["assets/styles/content.css"],
      "run_at": "document_end"
    }
  ],
  
  "web_accessible_resources": [
    "assets/styles/*",
    "assets/icons/*"
  ],
  
  "options_ui": {
    "page": "settings/settings.html",
    "open_in_tab": true
  },
  
  "icons": {
    "16": "assets/icons/icon.svg",
    "48": "assets/icons/icon.svg",
    "128": "assets/icons/icon.svg"
  },
  
  "browser_action": {
    "default_title": "YouTube视频总结器 - 打开设置",
    "default_icon": {
      "16": "assets/icons/icon.svg",
      "48": "assets/icons/icon.svg"
    }
  }
}
```

## 主要修改内容

### 1. 移除无效配置
- ❌ 删除 `popup/summary-modal.html` 引用
- ❌ 删除 `popup/summary-modal.css` 引用
- ❌ 删除 `browser_action.default_popup` 配置

### 2. 简化图标配置
- 使用现有的 `icon.svg` 文件
- 统一图标引用，避免缺失文件问题

### 3. 清理资源配置
- 保留必要的 `assets/styles/*` 和 `assets/icons/*`
- 移除不存在的popup资源

### 4. Browser Action 调整
- 保留图标和标题
- 点击扩展图标将打开设置页面（通过background脚本处理）

## 实施步骤

1. **切换到Code模式**
2. **更新manifest.json文件**
3. **验证所有引用的文件都存在**
4. **测试扩展加载和功能**

## 验证清单

- [ ] manifest.json 语法正确
- [ ] 所有引用的文件都存在
- [ ] 扩展可以正常加载
- [ ] YouTube页面注入功能正常
- [ ] 设置页面可以正常打开
- [ ] API配置和总结功能正常

## 注意事项

- 当前实现使用内容脚本在YouTube页面注入UI
- Browser action 主要用于快速访问设置页面
- 所有核心功能通过内容脚本实现，不依赖popup