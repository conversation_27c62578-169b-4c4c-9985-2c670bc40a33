/**
 * YouTube页面按钮注入模块
 * 负责在YouTube视频页面注入总结按钮和相关UI元素
 */

class YoutubeInjector {
  constructor() {
    this.summaryButton = null;
    this.modal = null;
    this.isInjected = false;
    this.currentVideoId = null;
    this.observer = null;
    this.isInitialized = false;
    this.initializationPromise = null;
  }

  /**
   * 初始化注入器
   */
  init() {
    console.log('初始化YouTube注入器');
    
    // 防止重复初始化
    if (this.isInitialized) {
      console.log('YouTube注入器已初始化，跳过');
      return Promise.resolve();
    }
    
    // 如果正在初始化，返回现有的Promise
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }
  
  async performInitialization() {
    try {
      // 等待页面和依赖完全加载
      await this.waitForPageReady();
      
      // 注入按钮
      const injectionSuccess = await this.injectButton();
      
      // 即使注入失败也继续初始化监听器，因为页面可能稍后变化
      if (!injectionSuccess) {
        console.warn('初始按钮注入失败，但继续初始化监听器');
      }

      // 监听页面变化（YouTube是SPA）
      this.observePageChanges();
      
      // 监听URL变化
      this.observeUrlChanges();
      
      this.isInitialized = true;
      console.log('YouTube注入器初始化完成');
    } catch (error) {
      console.error('YouTube注入器初始化失败:', error);
      this.initializationPromise = null; // 重置以允许重试
      this.isInitialized = false;
    }
  }
  
  async waitForPageReady() {
    return new Promise((resolve) => {
      const checkReady = () => {
        if (document.readyState === 'complete' &&
            document.querySelector('ytd-app') &&
            window.location.pathname === '/watch') {
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
    });
  }

  /**
   * 注入总结按钮
   */
  async injectButton() {
    try {
      // 检查是否在视频页面
      if (!this.isVideoPage()) {
        console.log('不在视频页面，跳过按钮注入');
        this.isInjected = false;
        return false;
      }

      // 获取当前视频ID
      const videoId = this.getCurrentVideoId();
      if (!videoId) {
        console.log('无法获取视频ID');
        this.isInjected = false;
        return false;
      }

      // 如果已经注入且是同一个视频，跳过
      if (this.isInjected && this.currentVideoId === videoId) {
        console.log('按钮已存在，跳过注入');
        return true;
      }

      // 清理之前的按钮
      this.cleanup();

      // 等待目标容器加载
      const container = await this.waitForContainer();
      if (!container) {
        console.log('未找到合适的容器');
        this.isInjected = false;
        return false;
      }

      // 创建总结按钮
      this.createSummaryButton(container);
      
      // 创建模态窗口
      this.createModal();

      this.isInjected = true;
      this.currentVideoId = videoId;
      
      console.log('总结按钮注入成功');
      return true;

    } catch (error) {
      console.error('注入按钮失败:', error);
      this.isInjected = false;
      return false;
    }
  }

  /**
   * 等待目标容器出现
   * @returns {Promise<Element|null>} 容器元素
   */
  waitForContainer() {
    return new Promise((resolve) => {
      const selectors = [
        '#top-level-buttons-computed', // 新版YouTube
        '#top-level-buttons', // 旧版YouTube
        '.ytd-menu-renderer', // 备选位置
        '#actions', // 更旧版本
        '.ytd-video-secondary-info-renderer' // 最后备选
      ];

      const checkContainer = () => {
        for (const selector of selectors) {
          const container = document.querySelector(selector);
          if (container) {
            console.log('找到容器:', selector);
            return resolve(container);
          }
        }

        // 如果没找到，继续等待
        setTimeout(checkContainer, 500);
      };

      checkContainer();
      
      // 10秒超时
      setTimeout(() => resolve(null), 10000);
    });
  }

  /**
   * 创建总结按钮
   * @param {Element} container - 容器元素
   */
  createSummaryButton(container) {
    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.className = 'yt-summarizer-button-container';
    buttonContainer.style.cssText = `
      display: inline-flex;
      align-items: center;
      margin-left: 8px;
      margin-right: 8px;
    `;

    // 创建按钮
    const button = document.createElement('button');
    button.className = 'yt-summarizer-button';
    button.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
      </svg>
      <span style="margin-left: 6px;">AI总结</span>
    `;
    
    button.style.cssText = `
      display: flex;
      align-items: center;
      padding: 8px 16px;
      background: #065fd4;
      color: white;
      border: none;
      border-radius: 18px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      font-family: "YouTube Sans", "Roboto", sans-serif;
    `;

    // 添加悬停效果
    button.addEventListener('mouseenter', () => {
      button.style.background = '#0b57d0';
      button.style.transform = 'scale(1.02)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.background = '#065fd4';
      button.style.transform = 'scale(1)';
    });

    // 添加点击事件
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.handleSummaryClick();
    });

    buttonContainer.appendChild(button);
    
    // 插入到容器中
    if (container.firstChild) {
      container.insertBefore(buttonContainer, container.firstChild);
    } else {
      container.appendChild(buttonContainer);
    }

    this.summaryButton = button;
  }

  /**
   * 创建模态窗口
   */
  createModal() {
    // 创建模态窗口背景
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'yt-summarizer-modal-overlay';
    modalOverlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 10000;
      display: none;
      justify-content: center;
      align-items: center;
    `;

    // 创建模态窗口内容
    const modalContent = document.createElement('div');
    modalContent.className = 'yt-summarizer-modal-content';
    modalContent.style.cssText = `
      background: white;
      border-radius: 12px;
      width: 90%;
      max-width: 800px;
      max-height: 80%;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
    `;

    // 创建模态窗口头部
    const modalHeader = document.createElement('div');
    modalHeader.className = 'yt-summarizer-modal-header';
    modalHeader.style.cssText = `
      padding: 20px 24px;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f8f9fa;
    `;

    const modalTitle = document.createElement('h2');
    modalTitle.textContent = 'AI视频总结';
    modalTitle.style.cssText = `
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #202124;
    `;

    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.style.cssText = `
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #5f6368;
      padding: 4px;
      border-radius: 4px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    `;

    closeButton.addEventListener('click', () => this.hideModal());

    modalHeader.appendChild(modalTitle);
    modalHeader.appendChild(closeButton);

    // 创建模态窗口主体
    const modalBody = document.createElement('div');
    modalBody.className = 'yt-summarizer-modal-body';
    modalBody.style.cssText = `
      padding: 24px;
      overflow-y: auto;
      flex: 1;
      min-height: 200px;
    `;

    // 创建加载状态
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'yt-summarizer-loading';
    loadingDiv.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      text-align: center;
    `;

    loadingDiv.innerHTML = `
      <div style="
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #065fd4;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      "></div>
      <p style="margin: 0; color: #5f6368; font-size: 16px;">正在生成总结...</p>
    `;

    // 添加旋转动画
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    // 创建内容区域
    const contentDiv = document.createElement('div');
    contentDiv.className = 'yt-summarizer-content';
    contentDiv.style.display = 'none';

    modalBody.appendChild(loadingDiv);
    modalBody.appendChild(contentDiv);

    modalContent.appendChild(modalHeader);
    modalContent.appendChild(modalBody);
    modalOverlay.appendChild(modalContent);

    // 点击背景关闭模态窗口
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        this.hideModal();
      }
    });

    // 添加到页面
    document.body.appendChild(modalOverlay);
    this.modal = modalOverlay;
  }

  /**
   * 处理总结按钮点击
   */
  async handleSummaryClick() {
    try {
      // 显示模态窗口
      this.showModal();

      // 更新按钮状态
      this.updateButtonState('loading');

      // 检查依赖是否就绪
      if (typeof transcriptExtractor === 'undefined') {
        throw new Error('字幕提取器未初始化，请刷新页面重试');
      }

      // 提取字幕
      console.log('开始提取字幕...');
      this.updateLoadingMessage('正在提取视频字幕...');

      const transcriptResult = await transcriptExtractor.extractFromCurrentPage();

      if (!transcriptResult.success) {
        throw new Error(transcriptResult.error || '字幕提取失败');
      }

      console.log('字幕提取成功，字幕长度:', transcriptResult.transcript.length);
      this.updateLoadingMessage('字幕提取成功，正在生成总结...');

      // 获取用户设置
      const settings = await this.getSettings();
      const templates = await this.getTemplates();

      if (!templates || templates.length === 0) {
        throw new Error('未找到总结模板，请检查设置');
      }

      const defaultTemplate = templates.find(t => t.id === settings.defaultTemplate) || templates[0];

      if (!defaultTemplate || !defaultTemplate.template) {
        throw new Error('总结模板无效，请检查设置');
      }

      // 生成总结
      this.updateLoadingMessage('正在调用AI服务生成总结...');

      const summaryResult = await this.generateSummary({
        content: transcriptResult.transcript,
        template: defaultTemplate.template,
        provider: settings.defaultProvider,
        videoId: transcriptResult.videoId,
        videoTitle: transcriptResult.videoTitle
      });

      if (!summaryResult.success) {
        throw new Error(summaryResult.error || '总结生成失败');
      }

      console.log('总结生成成功');

      // 显示总结结果
      this.displaySummary(summaryResult, transcriptResult);

      // 更新按钮状态
      this.updateButtonState('success');

    } catch (error) {
      console.error('生成总结失败:', error);

      // 提供更详细的错误信息和解决建议
      let errorMessage = error.message;
      let suggestions = [];

      if (error.message.includes('字幕')) {
        suggestions.push('确保视频有可用的字幕');
        suggestions.push('尝试手动启用字幕');
      } else if (error.message.includes('API') || error.message.includes('密钥')) {
        suggestions.push('检查API配置是否正确');
        suggestions.push('验证API密钥是否有效');
      } else if (error.message.includes('网络') || error.message.includes('连接')) {
        suggestions.push('检查网络连接');
        suggestions.push('稍后重试');
      }

      this.displayError(errorMessage, suggestions);
      this.updateButtonState('error');
    }
  }

  /**
   * 更新加载消息
   * @param {string} message - 消息内容
   */
  updateLoadingMessage(message) {
    if (this.modal) {
      const loadingDiv = this.modal.querySelector('.yt-summarizer-loading p');
      if (loadingDiv) {
        loadingDiv.textContent = message;
      }
    }
  }

  /**
   * 显示模态窗口
   */
  showModal() {
    if (this.modal) {
      this.modal.style.display = 'flex';
      
      // 重置状态
      const loading = this.modal.querySelector('.yt-summarizer-loading');
      const content = this.modal.querySelector('.yt-summarizer-content');
      
      if (loading) loading.style.display = 'flex';
      if (content) content.style.display = 'none';
    }
  }

  /**
   * 隐藏模态窗口
   */
  hideModal() {
    if (this.modal) {
      this.modal.style.display = 'none';
    }
  }

  /**
   * 显示总结结果
   * @param {Object} summaryResult - 总结结果
   * @param {Object} transcriptResult - 字幕结果
   */
  displaySummary(summaryResult, transcriptResult) {
    const loading = this.modal.querySelector('.yt-summarizer-loading');
    const content = this.modal.querySelector('.yt-summarizer-content');
    
    if (loading) loading.style.display = 'none';
    if (content) {
      content.style.display = 'block';
      content.innerHTML = `
        <div style="margin-bottom: 16px; padding: 12px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #34a853;">
          <strong>✅ 总结生成成功</strong>
          <div style="font-size: 12px; color: #5f6368; margin-top: 4px;">
            视频: ${transcriptResult.videoTitle} | 
            提供商: ${summaryResult.provider} | 
            字幕来源: ${transcriptResult.source}
          </div>
        </div>
        
        <div style="line-height: 1.6; color: #202124; white-space: pre-wrap;">${summaryResult.summary}</div>
        
        <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e0e0e0; display: flex; gap: 8px;">
          <button onclick="navigator.clipboard.writeText('${summaryResult.summary.replace(/'/g, "\\'")}'); this.textContent='已复制!'; setTimeout(() => this.textContent='复制文本', 2000)" 
                  style="padding: 8px 16px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
            复制文本
          </button>
          <button onclick="window.open('data:text/plain;charset=utf-8,' + encodeURIComponent('${summaryResult.summary.replace(/'/g, "\\'")}'))" 
                  style="padding: 8px 16px; background: #388e3c; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
            导出文本
          </button>
        </div>
      `;
    }
  }

  /**
   * 显示错误信息
   * @param {string} errorMessage - 错误消息
   * @param {Array} suggestions - 解决建议
   */
  displayError(errorMessage, suggestions = []) {
    const loading = this.modal.querySelector('.yt-summarizer-loading');
    const content = this.modal.querySelector('.yt-summarizer-content');

    if (loading) loading.style.display = 'none';
    if (content) {
      content.style.display = 'block';

      let suggestionsHtml = '';
      if (suggestions.length > 0) {
        suggestionsHtml = `
          <div style="margin: 16px 0; text-align: left;">
            <h4 style="color: #5f6368; margin: 0 0 8px 0; font-size: 14px;">解决建议:</h4>
            <ul style="color: #5f6368; margin: 0; padding-left: 20px; font-size: 13px;">
              ${suggestions.map(s => `<li>${s}</li>`).join('')}
            </ul>
          </div>
        `;
      }

      content.innerHTML = `
        <div style="padding: 20px; text-align: center;">
          <div style="color: #d93025; font-size: 48px; margin-bottom: 16px;">⚠️</div>
          <h3 style="color: #d93025; margin: 0 0 12px 0;">生成失败</h3>
          <p style="color: #5f6368; margin: 0 0 20px 0; word-break: break-word;">${errorMessage}</p>
          ${suggestionsHtml}
          <div style="display: flex; gap: 8px; justify-content: center; flex-wrap: wrap;">
            <button onclick="location.reload()"
                    style="padding: 8px 16px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
              刷新页面
            </button>
            <button onclick="browser.runtime.openOptionsPage()"
                    style="padding: 8px 16px; background: #5f6368; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
              打开设置
            </button>
            <button onclick="this.closest('.yt-summarizer-modal-overlay').style.display='none'"
                    style="padding: 8px 16px; background: #34a853; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
              重试
            </button>
          </div>
        </div>
      `;
    }
  }

  /**
   * 更新按钮状态
   * @param {string} state - 状态 (normal, loading, success, error)
   */
  updateButtonState(state) {
    if (!this.summaryButton) return;

    const button = this.summaryButton;
    
    switch (state) {
      case 'loading':
        button.disabled = true;
        button.innerHTML = `
          <div style="width: 16px; height: 16px; border: 2px solid transparent; border-top: 2px solid currentColor; border-radius: 50%; animation: spin 1s linear infinite;"></div>
          <span style="margin-left: 6px;">生成中...</span>
        `;
        button.style.background = '#5f6368';
        break;
        
      case 'success':
        button.disabled = false;
        button.innerHTML = `
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
          </svg>
          <span style="margin-left: 6px;">总结完成</span>
        `;
        button.style.background = '#34a853';
        setTimeout(() => this.updateButtonState('normal'), 3000);
        break;
        
      case 'error':
        button.disabled = false;
        button.innerHTML = `
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M13,13H11V7H13M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
          </svg>
          <span style="margin-left: 6px;">生成失败</span>
        `;
        button.style.background = '#ea4335';
        setTimeout(() => this.updateButtonState('normal'), 5000);
        break;
        
      default: // normal
        button.disabled = false;
        button.innerHTML = `
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          <span style="margin-left: 6px;">AI总结</span>
        `;
        button.style.background = '#065fd4';
        break;
    }
  }

  /**
   * 获取用户设置
   * @returns {Promise<Object>} 设置对象
   */
  async getSettings() {
    try {
      const response = await browser.runtime.sendMessage({ action: 'getSettings' });
      return response.success ? response.data : {
        defaultProvider: 'deepseek',
        defaultTemplate: 'basic'
      };
    } catch (error) {
      console.error('获取设置失败:', error);
      return {
        defaultProvider: 'deepseek',
        defaultTemplate: 'basic'
      };
    }
  }

  /**
   * 获取模板列表
   * @returns {Promise<Array>} 模板数组
   */
  async getTemplates() {
    try {
      const response = await browser.runtime.sendMessage({ action: 'getTemplates' });
      return response.success ? response.data : [];
    } catch (error) {
      console.error('获取模板失败:', error);
      return [{
        id: 'basic',
        name: '基础总结',
        template: '请总结以下视频内容的要点：\n\n{content}',
        isDefault: true
      }];
    }
  }

  /**
   * 生成总结
   * @param {Object} data - 总结数据
   * @returns {Promise<Object>} 总结结果
   */
  async generateSummary(data) {
    try {
      const response = await browser.runtime.sendMessage({
        action: 'generateSummary',
        data: data
      });
      return response;
    } catch (error) {
      console.error('生成总结失败:', error);
      return {
        success: false,
        error: '无法连接到后台服务: ' + error.message
      };
    }
  }

  /**
   * 检查是否在视频页面
   * @returns {boolean} 是否在视频页面
   */
  isVideoPage() {
    return window.location.pathname === '/watch' && window.location.search.includes('v=');
  }

  /**
   * 获取当前视频ID
   * @returns {string|null} 视频ID
   */
  getCurrentVideoId() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('v');
  }

  /**
   * 监听页面变化
   */
  observePageChanges() {
    // 监听DOM变化
    this.observer = new MutationObserver((mutations) => {
      let shouldReinject = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查是否有重要的DOM变化
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.matches && (
                node.matches('#top-level-buttons-computed') ||
                node.matches('#top-level-buttons') ||
                node.matches('.ytd-video-secondary-info-renderer')
              )) {
                shouldReinject = true;
              }
            }
          });
        }
      });
      
      if (shouldReinject) {
        setTimeout(() => this.injectButton(), 1000);
      }
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 监听URL变化
   */
  observeUrlChanges() {
    let currentUrl = window.location.href;
    
    const checkUrlChange = () => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        console.log('URL变化，重新注入按钮');
        setTimeout(() => this.injectButton(), 1000);
      }
    };

    // 监听popstate事件
    window.addEventListener('popstate', checkUrlChange);
    
    // 定期检查URL变化（YouTube的SPA导航）
    setInterval(checkUrlChange, 2000);
  }

  /**
   * 清理之前的注入内容
   */
  cleanup() {
    // 移除之前的按钮
    const existingButtons = document.querySelectorAll('.yt-summarizer-button-container');
    existingButtons.forEach(button => button.remove());

    // 移除之前的模态窗口
    const existingModals = document.querySelectorAll('.yt-summarizer-modal-overlay');
    existingModals.forEach(modal => modal.remove());

    this.summaryButton = null;
    this.modal = null;
    this.isInjected = false;
  }

  /**
   * 销毁注入器
   */
  destroy() {
    this.cleanup();
    
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
}

// 创建全局实例
const youtubeInjector = new YoutubeInjector();