/**
 * 内容脚本主文件
 * 负责协调各个模块的工作
 */

(function() {
  'use strict';

  console.log('YouTube总结器内容脚本已加载');

  // 等待页面完全加载
  async function initializeExtension() {
    // 检查是否在YouTube视频页面
    if (!window.location.hostname.includes('youtube.com')) {
      console.log('不在YouTube页面，退出');
      return;
    }

    console.log('在YouTube页面，开始初始化');

    // 初始化YouTube注入器
    if (typeof youtubeInjector !== 'undefined') {
      try {
        await youtubeInjector.init();
        console.log('YouTube注入器初始化成功');
      } catch (error) {
        console.error('YouTube注入器初始化失败:', error);
      }
    } else {
      console.error('YouTube注入器未加载');
    }
  }

  // 监听来自后台脚本的消息
  browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('内容脚本收到消息:', message);

    // 处理异步消息
    const handleMessage = async () => {
      switch (message.action) {
        case 'pageLoaded':
          // 页面加载完成，等待依赖加载后重新初始化
          console.log('收到pageLoaded消息，等待依赖加载...');
          const dependenciesLoaded = await waitForDependencies(5000);
          if (dependenciesLoaded) {
            setTimeout(async () => {
              try {
                await initializeExtension();
              } catch (error) {
                console.error('延迟初始化失败:', error);
              }
            }, 1000);
            return { success: true };
          } else {
            return { success: false, error: '依赖加载超时' };
          }

        case 'triggerSummary':
          // 触发总结功能
          if (typeof youtubeInjector !== 'undefined') {
            // 如果注入器存在但未注入，先尝试注入
            if (!youtubeInjector.isInjected) {
              console.log('注入器未注入，尝试重新注入...');
              await youtubeInjector.injectButton();
            }
            
            // 检查是否成功注入或已经注入
            if (youtubeInjector.isInjected) {
              youtubeInjector.handleSummaryClick();
              return { success: true };
            } else {
              return { success: false, error: '注入器初始化失败，请刷新页面重试' };
            }
          } else {
            return { success: false, error: '注入器未加载，请刷新页面重试' };
          }

        case 'extractTranscript':
          // 提取字幕
          if (typeof transcriptExtractor !== 'undefined') {
            try {
              const result = await transcriptExtractor.extractFromCurrentPage(message.options || {});
              return result;
            } catch (error) {
              return { success: false, error: error.message };
            }
          } else {
            return { success: false, error: '字幕提取器未初始化' };
          }

        case 'checkVideoPage':
          // 检查是否在视频页面
          const isVideoPage = window.location.pathname === '/watch' &&
                             window.location.search.includes('v=');
          const videoId = isVideoPage ? new URLSearchParams(window.location.search).get('v') : null;
          
          return {
            success: true,
            isVideoPage: isVideoPage,
            videoId: videoId,
            url: window.location.href,
            dependenciesLoaded: checkDependencies(),
            injectorReady: typeof youtubeInjector !== 'undefined' && youtubeInjector.isInjected
          };

        case 'getVideoInfo':
          // 获取视频信息
          const videoInfo = getVideoInfo();
          return { success: true, title: videoInfo.title, ...videoInfo };

        case 'clearCache':
          // 清除缓存
          if (typeof transcriptExtractor !== 'undefined') {
            transcriptExtractor.clearCache();
          }
          return { success: true };

        default:
          console.warn('未知的消息类型:', message.action);
          return { success: false, error: '未知的消息类型' };
      }
    };

    // 执行异步处理并发送响应
    handleMessage().then(sendResponse).catch(error => {
      console.error('消息处理异常:', error);
      sendResponse({ success: false, error: error.message });
    });

    // 返回true表示异步响应
    return true;
  });

  /**
   * 获取视频信息
   * @returns {Object} 视频信息
   */
  function getVideoInfo() {
    const videoId = new URLSearchParams(window.location.search).get('v');
    
    // 获取视频标题
    const titleSelectors = [
      'h1.title.style-scope.ytd-video-primary-info-renderer',
      'h1.ytd-video-primary-info-renderer',
      '.ytd-video-primary-info-renderer h1',
      'h1[class*="title"]'
    ];
    
    let title = '未知标题';
    for (const selector of titleSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent) {
        title = element.textContent.trim();
        break;
      }
    }

    // 获取频道信息
    const channelSelectors = [
      '.ytd-video-owner-renderer .ytd-channel-name a',
      '.ytd-channel-name a',
      '#owner-name a',
      '.yt-user-info a'
    ];
    
    let channelName = '未知频道';
    let channelUrl = '';
    for (const selector of channelSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        channelName = element.textContent.trim();
        channelUrl = element.href || '';
        break;
      }
    }

    // 获取视频时长
    let duration = '';
    const durationElement = document.querySelector('.ytp-time-duration');
    if (durationElement) {
      duration = durationElement.textContent.trim();
    }

    // 获取观看次数
    let viewCount = '';
    const viewSelectors = [
      '.view-count',
      '.ytd-video-view-count-renderer',
      '#count .ytd-video-view-count-renderer'
    ];
    
    for (const selector of viewSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent) {
        viewCount = element.textContent.trim();
        break;
      }
    }

    // 获取发布时间
    let publishDate = '';
    const dateSelectors = [
      '#date .ytd-video-secondary-info-renderer',
      '.ytd-video-secondary-info-renderer #date',
      '#info-strings .ytd-video-secondary-info-renderer'
    ];
    
    for (const selector of dateSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent) {
        publishDate = element.textContent.trim();
        break;
      }
    }

    return {
      videoId: videoId,
      title: title,
      channelName: channelName,
      channelUrl: channelUrl,
      duration: duration,
      viewCount: viewCount,
      publishDate: publishDate,
      url: window.location.href
    };
  }

  /**
   * 检查必要的依赖是否加载
   * @returns {boolean} 是否所有依赖都已加载
   */
  function checkDependencies() {
    const dependencies = [
      'YoutubeTranscript',
      'transcriptExtractor', 
      'youtubeInjector'
    ];

    const missing = dependencies.filter(dep => typeof window[dep] === 'undefined');
    
    if (missing.length > 0) {
      console.warn('缺少依赖:', missing);
      return false;
    }

    return true;
  }

  /**
   * 等待依赖加载完成
   * @param {number} maxWait - 最大等待时间(毫秒)
   * @returns {Promise<boolean>} 是否加载成功
   */
  function waitForDependencies(maxWait = 10000) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let retryCount = 0;
      const maxRetries = 3;

      const checkInterval = setInterval(() => {
        const dependenciesReady = checkDependencies();
        const elapsed = Date.now() - startTime;

        if (dependenciesReady) {
          clearInterval(checkInterval);
          console.log('所有依赖加载完成');
          resolve(true);
        } else if (elapsed > maxWait) {
          clearInterval(checkInterval);
          console.error('依赖加载超时，已等待:', elapsed + 'ms');

          // 尝试强制重新加载依赖
          if (retryCount < maxRetries) {
            retryCount++;
            console.log(`尝试重新加载依赖 (${retryCount}/${maxRetries})`);
            setTimeout(() => {
              waitForDependencies(maxWait).then(resolve);
            }, 1000);
          } else {
            resolve(false);
          }
        }
      }, 100);
    });
  }

  /**
   * 主初始化函数
   */
  async function main() {
    try {
      console.log('开始初始化YouTube总结器...');

      // 等待依赖加载
      const dependenciesLoaded = await waitForDependencies();
      if (!dependenciesLoaded) {
        console.error('依赖加载失败，无法初始化扩展');
        return;
      }

      // 等待DOM加载完成
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }

      // 额外等待YouTube页面完全加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 初始化扩展
      await initializeExtension();

      console.log('YouTube总结器初始化完成');

    } catch (error) {
      console.error('初始化失败:', error);
    }
  }

  // 监听页面卸载，清理资源
  window.addEventListener('beforeunload', () => {
    console.log('页面即将卸载，清理资源');
    
    if (typeof youtubeInjector !== 'undefined') {
      youtubeInjector.destroy();
    }
    
    if (typeof transcriptExtractor !== 'undefined') {
      transcriptExtractor.clearCache();
    }
  });

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      console.log('页面变为可见，检查是否需要重新初始化');
      // 延迟检查，确保页面完全加载
      setTimeout(() => {
        const currentVideoId = new URLSearchParams(window.location.search).get('v');
        if (currentVideoId && typeof youtubeInjector !== 'undefined') {
          // 如果视频ID变化了，重新注入按钮
          if (youtubeInjector.currentVideoId !== currentVideoId) {
            youtubeInjector.injectButton();
          }
        }
      }, 1000);
    }
  });

  // 监听键盘快捷键
  document.addEventListener('keydown', (event) => {
    // Ctrl+Shift+S 触发总结
    if (event.ctrlKey && event.shiftKey && event.key === 'S') {
      event.preventDefault();
      
      if (typeof youtubeInjector !== 'undefined') {
        youtubeInjector.handleSummaryClick();
      }
    }
    
    // Ctrl+Shift+C 清除缓存
    if (event.ctrlKey && event.shiftKey && event.key === 'C') {
      event.preventDefault();
      
      if (typeof transcriptExtractor !== 'undefined') {
        transcriptExtractor.clearCache();
        console.log('缓存已清除');
      }
    }
  });

  // 开始初始化
  main();

})();