/**
 * 字幕提取模块
 * 负责从YouTube页面提取视频字幕
 */

class TranscriptExtractor {
  constructor() {
    this.currentVideoId = null;
    this.cachedTranscript = null;
    this.isExtracting = false;
  }

  /**
   * 从当前YouTube页面提取视频字幕
   * @param {Object} options - 提取选项
   * @returns {Promise<Object>} 提取结果
   */
  async extractFromCurrentPage(options = {}) {
    try {
      // 获取当前视频ID
      const videoId = this.getCurrentVideoId();
      if (!videoId) {
        throw new Error('无法获取视频ID，请确保在YouTube视频页面');
      }

      // 检查是否已有缓存的字幕
      if (this.currentVideoId === videoId && this.cachedTranscript) {
        console.log('使用缓存的字幕数据');
        return {
          success: true,
          videoId: videoId,
          videoTitle: this.getVideoTitle(),
          transcript: this.cachedTranscript,
          source: 'cache'
        };
      }

      // 防止重复提取
      if (this.isExtracting) {
        throw new Error('正在提取字幕，请稍候...');
      }

      this.isExtracting = true;
      console.log('开始提取视频字幕:', videoId);

      // 尝试多种提取方法
      let transcript = null;
      let source = '';

      // 方法1: 使用YouTube Transcript API
      try {
        transcript = await this.extractUsingTranscriptAPI(videoId, options);
        source = 'transcript-api';
        console.log('使用Transcript API成功提取字幕');
      } catch (error) {
        console.log('Transcript API提取失败:', error.message);
      }

      // 方法2: 从页面DOM提取
      if (!transcript) {
        try {
          transcript = await this.extractFromPageDOM();
          source = 'page-dom';
          console.log('从页面DOM成功提取字幕');
        } catch (error) {
          console.log('页面DOM提取失败:', error.message);
        }
      }

      // 方法3: 从YouTube内部API提取
      if (!transcript) {
        try {
          transcript = await this.extractFromInternalAPI(videoId);
          source = 'internal-api';
          console.log('从内部API成功提取字幕');
        } catch (error) {
          console.log('内部API提取失败:', error.message);
        }
      }

      if (!transcript || transcript.length === 0) {
        throw new Error('该视频没有可用的字幕，或字幕提取失败');
      }

      // 处理和格式化字幕
      const processedTranscript = this.processTranscript(transcript);
      
      // 缓存结果
      this.currentVideoId = videoId;
      this.cachedTranscript = processedTranscript;

      return {
        success: true,
        videoId: videoId,
        videoTitle: this.getVideoTitle(),
        transcript: processedTranscript,
        source: source,
        itemCount: transcript.length
      };

    } catch (error) {
      console.error('字幕提取失败:', error);
      return {
        success: false,
        error: error.message,
        videoId: this.getCurrentVideoId()
      };
    } finally {
      this.isExtracting = false;
    }
  }

  /**
   * 使用YouTube Transcript API提取字幕
   * @param {string} videoId - 视频ID
   * @param {Object} options - 选项
   * @returns {Promise<Array>} 字幕数组
   */
  async extractUsingTranscriptAPI(videoId, options) {
    if (typeof YoutubeTranscript === 'undefined') {
      throw new Error('YoutubeTranscript库未加载');
    }

    const transcript = await YoutubeTranscript.fetchTranscript(videoId, {
      lang: options.language || 'zh'
    });

    if (!transcript || transcript.length === 0) {
      throw new Error('API返回空字幕');
    }

    return transcript;
  }

  /**
   * 从页面DOM提取字幕
   * @returns {Promise<Array>} 字幕数组
   */
  async extractFromPageDOM() {
    // 查找页面中的字幕元素
    const captionElements = document.querySelectorAll('.ytp-caption-segment, .captions-text, .caption-window');
    
    if (captionElements.length === 0) {
      throw new Error('页面中未找到字幕元素');
    }

    const transcript = [];
    let currentTime = 0;

    captionElements.forEach((element, index) => {
      const text = element.textContent.trim();
      if (text) {
        transcript.push({
          text: text,
          start: currentTime,
          duration: 3, // 估算持续时间
          end: currentTime + 3
        });
        currentTime += 3;
      }
    });

    if (transcript.length === 0) {
      throw new Error('未能从页面提取到有效字幕');
    }

    return transcript;
  }

  /**
   * 从YouTube内部API提取字幕
   * @param {string} videoId - 视频ID
   * @returns {Promise<Array>} 字幕数组
   */
  async extractFromInternalAPI(videoId) {
    // 尝试从页面的JavaScript变量中获取字幕数据
    const scripts = document.querySelectorAll('script');
    
    for (const script of scripts) {
      const content = script.textContent;
      
      // 查找包含字幕信息的JSON数据
      const patterns = [
        /ytInitialPlayerResponse\s*=\s*({.*?});/,
        /"captions":({.*?}),/,
        /"playerCaptionsTracklistRenderer":({.*?})/
      ];

      for (const pattern of patterns) {
        const match = content.match(pattern);
        if (match) {
          try {
            const data = JSON.parse(match[1]);
            const captionsData = this.findCaptionsInPlayerData(data);
            
            if (captionsData && captionsData.length > 0) {
              // 获取第一个可用的字幕轨道
              const captionTrack = captionsData[0];
              const transcript = await this.fetchCaptionFromUrl(captionTrack.baseUrl);
              return transcript;
            }
          } catch (e) {
            continue;
          }
        }
      }
    }

    throw new Error('未能从内部API获取字幕数据');
  }

  /**
   * 在播放器数据中查找字幕信息
   * @param {Object} data - 播放器数据
   * @returns {Array|null} 字幕轨道数组
   */
  findCaptionsInPlayerData(data) {
    const findCaptions = (obj) => {
      if (!obj || typeof obj !== 'object') return null;

      if (obj.captionTracks && Array.isArray(obj.captionTracks)) {
        return obj.captionTracks;
      }

      if (obj.playerCaptionsTracklistRenderer && obj.playerCaptionsTracklistRenderer.captionTracks) {
        return obj.playerCaptionsTracklistRenderer.captionTracks;
      }

      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const result = findCaptions(obj[key]);
          if (result) return result;
        }
      }

      return null;
    };

    return findCaptions(data);
  }

  /**
   * 从字幕URL获取字幕内容
   * @param {string} url - 字幕URL
   * @returns {Promise<Array>} 字幕数组
   */
  async fetchCaptionFromUrl(url) {
    try {
      const response = await fetch(url);
      const xmlContent = await response.text();
      return this.parseXMLTranscript(xmlContent);
    } catch (error) {
      throw new Error(`获取字幕内容失败: ${error.message}`);
    }
  }

  /**
   * 解析XML格式的字幕
   * @param {string} xmlContent - XML内容
   * @returns {Array} 字幕数组
   */
  parseXMLTranscript(xmlContent) {
    const transcript = [];
    const textPattern = /<text[^>]*start="([^"]*)"[^>]*dur="([^"]*)"[^>]*>([^<]*)<\/text>/g;
    let match;

    while ((match = textPattern.exec(xmlContent)) !== null) {
      const start = parseFloat(match[1]);
      const duration = parseFloat(match[2]);
      let text = match[3];

      // 解码HTML实体
      text = text.replace(/&amp;/g, '&')
                 .replace(/&lt;/g, '<')
                 .replace(/&gt;/g, '>')
                 .replace(/&quot;/g, '"')
                 .replace(/&#39;/g, "'");

      text = text.trim();
      if (text) {
        transcript.push({
          text: text,
          start: start,
          duration: duration,
          end: start + duration
        });
      }
    }

    return transcript;
  }

  /**
   * 处理和格式化字幕
   * @param {Array} rawTranscript - 原始字幕数组
   * @returns {string} 格式化后的字幕文本
   */
  processTranscript(rawTranscript) {
    if (!rawTranscript || rawTranscript.length === 0) {
      return '';
    }

    // 合并相近的字幕片段，去除重复
    const mergedTranscript = [];
    let currentText = '';
    let lastEndTime = 0;

    rawTranscript.forEach((item, index) => {
      const text = item.text.trim();
      if (!text) return;

      // 如果时间间隔很小，合并文本
      if (item.start - lastEndTime < 1 && currentText) {
        currentText += ' ' + text;
      } else {
        if (currentText) {
          mergedTranscript.push(currentText);
        }
        currentText = text;
      }

      lastEndTime = item.end || (item.start + (item.duration || 3));
    });

    // 添加最后一段
    if (currentText) {
      mergedTranscript.push(currentText);
    }

    // 去除重复的句子
    const uniqueTranscript = [];
    const seenTexts = new Set();

    mergedTranscript.forEach(text => {
      const normalizedText = text.toLowerCase().replace(/[^\w\s]/g, '');
      if (!seenTexts.has(normalizedText)) {
        seenTexts.add(normalizedText);
        uniqueTranscript.push(text);
      }
    });

    return uniqueTranscript.join(' ');
  }

  /**
   * 获取当前视频ID
   * @returns {string|null} 视频ID
   */
  getCurrentVideoId() {
    // 从URL获取
    const urlParams = new URLSearchParams(window.location.search);
    const videoId = urlParams.get('v');
    
    if (videoId) {
      return videoId;
    }

    // 从页面元素获取
    const videoElement = document.querySelector('video');
    if (videoElement && videoElement.src) {
      const match = videoElement.src.match(/[?&]v=([^&]+)/);
      if (match) {
        return match[1];
      }
    }

    return null;
  }

  /**
   * 获取视频标题
   * @returns {string} 视频标题
   */
  getVideoTitle() {
    // 尝试多种方式获取标题
    const selectors = [
      'h1.title.style-scope.ytd-video-primary-info-renderer',
      'h1.ytd-video-primary-info-renderer',
      '.ytd-video-primary-info-renderer h1',
      'h1[class*="title"]',
      'title'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent) {
        let title = element.textContent.trim();
        // 移除" - YouTube"后缀
        title = title.replace(/ - YouTube$/, '');
        if (title) {
          return title;
        }
      }
    }

    return '未知标题';
  }

  /**
   * 检查视频是否有字幕
   * @returns {Promise<boolean>} 是否有字幕
   */
  async hasTranscript() {
    try {
      const videoId = this.getCurrentVideoId();
      if (!videoId) return false;

      // 快速检查页面是否有字幕按钮
      const captionButton = document.querySelector('.ytp-subtitles-button, .ytp-caption-button');
      if (captionButton && !captionButton.classList.contains('ytp-button-disabled')) {
        return true;
      }

      // 尝试获取字幕数据
      const result = await this.extractFromCurrentPage();
      return result.success;
    } catch (error) {
      return false;
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.currentVideoId = null;
    this.cachedTranscript = null;
    console.log('字幕缓存已清除');
  }

  /**
   * 获取支持的语言列表
   * @returns {Array} 语言列表
   */
  getSupportedLanguages() {
    return [
      { code: 'zh', name: '中文', native: '中文' },
      { code: 'zh-CN', name: '中文(简体)', native: '中文(简体)' },
      { code: 'zh-TW', name: '中文(繁体)', native: '中文(繁體)' },
      { code: 'en', name: '英语', native: 'English' },
      { code: 'ja', name: '日语', native: '日本語' },
      { code: 'ko', name: '韩语', native: '한국어' },
      { code: 'es', name: '西班牙语', native: 'Español' },
      { code: 'fr', name: '法语', native: 'Français' },
      { code: 'de', name: '德语', native: 'Deutsch' },
      { code: 'auto', name: '自动检测', native: 'Auto' }
    ];
  }
}

// 创建全局实例
const transcriptExtractor = new TranscriptExtractor();