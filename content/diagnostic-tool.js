/**
 * 诊断工具
 * 用于检测和报告扩展运行状态
 */

class DiagnosticTool {
  constructor() {
    this.results = {};
  }

  /**
   * 运行完整诊断
   * @returns {Object} 诊断结果
   */
  async runFullDiagnostic() {
    console.log('开始运行扩展诊断...');
    
    this.results = {
      timestamp: new Date().toISOString(),
      browser: this.getBrowserInfo(),
      page: this.getPageInfo(),
      dependencies: this.checkDependencies(),
      youtube: await this.checkYouTubeFeatures(),
      api: await this.checkAPIConfiguration(),
      storage: await this.checkStorage(),
      permissions: this.checkPermissions()
    };

    this.logResults();
    return this.results;
  }

  /**
   * 获取浏览器信息
   */
  getBrowserInfo() {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    };
  }

  /**
   * 获取页面信息
   */
  getPageInfo() {
    return {
      url: window.location.href,
      hostname: window.location.hostname,
      pathname: window.location.pathname,
      search: window.location.search,
      isYouTube: window.location.hostname.includes('youtube.com'),
      isVideoPage: window.location.pathname === '/watch',
      videoId: new URLSearchParams(window.location.search).get('v'),
      readyState: document.readyState,
      title: document.title
    };
  }

  /**
   * 检查依赖加载状态
   */
  checkDependencies() {
    const dependencies = {
      YoutubeTranscript: typeof window.YoutubeTranscript !== 'undefined',
      transcriptExtractor: typeof window.transcriptExtractor !== 'undefined',
      youtubeInjector: typeof window.youtubeInjector !== 'undefined'
    };

    const allLoaded = Object.values(dependencies).every(loaded => loaded);
    
    return {
      individual: dependencies,
      allLoaded: allLoaded,
      missing: Object.keys(dependencies).filter(key => !dependencies[key])
    };
  }

  /**
   * 检查YouTube功能
   */
  async checkYouTubeFeatures() {
    const features = {
      videoElement: !!document.querySelector('video'),
      playerContainer: !!document.querySelector('#movie_player, .html5-video-player'),
      captionButton: !!document.querySelector('.ytp-subtitles-button, .ytp-caption-button'),
      captionElements: document.querySelectorAll('.ytp-caption-segment, .captions-text').length,
      injectedButton: !!document.querySelector('.yt-summarizer-button'),
      playerReady: this.isPlayerReady()
    };

    // 检查字幕可用性
    try {
      features.captionsAvailable = await this.checkCaptionsAvailable();
    } catch (error) {
      features.captionsAvailable = false;
      features.captionsError = error.message;
    }

    return features;
  }

  /**
   * 检查播放器是否就绪
   */
  isPlayerReady() {
    const video = document.querySelector('video');
    if (!video) return false;
    
    return video.readyState >= 2; // HAVE_CURRENT_DATA
  }

  /**
   * 检查字幕是否可用
   */
  async checkCaptionsAvailable() {
    if (typeof transcriptExtractor === 'undefined') {
      throw new Error('transcriptExtractor未加载');
    }

    try {
      const hasTranscript = await transcriptExtractor.hasTranscript();
      return hasTranscript;
    } catch (error) {
      throw new Error('检查字幕可用性失败: ' + error.message);
    }
  }

  /**
   * 检查API配置
   */
  async checkAPIConfiguration() {
    try {
      const response = await browser.runtime.sendMessage({ action: 'getApiConfig' });
      
      if (!response || !response.success) {
        return {
          configured: false,
          error: 'API配置获取失败'
        };
      }

      const config = response.data;
      return {
        configured: !!config,
        provider: config?.provider,
        hasApiKey: !!(config?.apiKey && config.apiKey.length > 0),
        hasBaseUrl: !!(config?.baseUrl && config.baseUrl.startsWith('http')),
        valid: !!(config?.provider && config?.apiKey && config?.baseUrl)
      };
    } catch (error) {
      return {
        configured: false,
        error: error.message
      };
    }
  }

  /**
   * 检查存储状态
   */
  async checkStorage() {
    try {
      const response = await browser.runtime.sendMessage({ action: 'getSettings' });
      
      return {
        accessible: !!response,
        hasSettings: !!(response?.success && response?.data),
        settingsValid: !!(response?.data?.defaultProvider && response?.data?.defaultTemplate)
      };
    } catch (error) {
      return {
        accessible: false,
        error: error.message
      };
    }
  }

  /**
   * 检查权限
   */
  checkPermissions() {
    return {
      activeTab: true, // 假设有权限，否则脚本不会运行
      storage: true,   // 假设有权限
      youtubeAccess: window.location.hostname.includes('youtube.com')
    };
  }

  /**
   * 输出诊断结果
   */
  logResults() {
    console.group('🔍 YouTube总结器扩展诊断报告');
    
    console.log('📊 基本信息:', this.results.browser);
    console.log('📄 页面信息:', this.results.page);
    console.log('📦 依赖状态:', this.results.dependencies);
    console.log('🎥 YouTube功能:', this.results.youtube);
    console.log('🔑 API配置:', this.results.api);
    console.log('💾 存储状态:', this.results.storage);
    console.log('🔐 权限状态:', this.results.permissions);
    
    // 生成问题报告
    const issues = this.generateIssueReport();
    if (issues.length > 0) {
      console.warn('⚠️ 发现的问题:', issues);
    } else {
      console.log('✅ 未发现明显问题');
    }
    
    console.groupEnd();
  }

  /**
   * 生成问题报告
   */
  generateIssueReport() {
    const issues = [];

    if (!this.results.page.isYouTube) {
      issues.push('不在YouTube页面');
    }

    if (!this.results.page.isVideoPage) {
      issues.push('不在YouTube视频页面');
    }

    if (!this.results.dependencies.allLoaded) {
      issues.push(`依赖未完全加载: ${this.results.dependencies.missing.join(', ')}`);
    }

    if (!this.results.youtube.videoElement) {
      issues.push('未找到视频元素');
    }

    if (!this.results.youtube.captionButton) {
      issues.push('未找到字幕按钮');
    }

    if (!this.results.api.configured) {
      issues.push('API未配置');
    }

    if (this.results.api.configured && !this.results.api.valid) {
      issues.push('API配置无效');
    }

    return issues;
  }

  /**
   * 获取诊断摘要
   */
  getSummary() {
    const issues = this.generateIssueReport();
    
    return {
      status: issues.length === 0 ? 'healthy' : 'issues',
      issueCount: issues.length,
      issues: issues,
      canSummarize: this.canSummarize()
    };
  }

  /**
   * 判断是否可以进行总结
   */
  canSummarize() {
    return this.results.page.isVideoPage &&
           this.results.dependencies.allLoaded &&
           this.results.youtube.videoElement &&
           this.results.api.valid;
  }
}

// 创建全局实例
const diagnosticTool = new DiagnosticTool();

// 添加快捷键触发诊断 (Ctrl+Shift+D)
document.addEventListener('keydown', (event) => {
  if (event.ctrlKey && event.shiftKey && event.key === 'D') {
    event.preventDefault();
    diagnosticTool.runFullDiagnostic();
  }
});
