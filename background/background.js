/**
 * 后台脚本主文件
 * 负责扩展的核心逻辑和消息处理
 */

// 扩展安装和启动时的初始化
browser.runtime.onInstalled.addListener(async (details) => {
  console.log('YouTube总结器扩展已安装');
  
  if (details.reason === 'install') {
    // 首次安装时的初始化
    await initializeExtension();
  } else if (details.reason === 'update') {
    // 更新时的处理
    console.log('扩展已更新到新版本');
  }
});

// 扩展启动时的初始化
browser.runtime.onStartup.addListener(async () => {
  console.log('YouTube总结器扩展已启动');
  await initializeExtension();
});

/**
 * 初始化扩展
 */
async function initializeExtension() {
  try {
    // 检查是否有保存的设置，如果没有则创建默认设置
    const settings = await storageManager.getSettings();
    if (!settings) {
      await storageManager.saveSettings(storageManager.getDefaultSettings());
      console.log('已创建默认设置');
    }

    // 检查是否有保存的模板，如果没有则创建默认模板
    const templates = await storageManager.getTemplates();
    if (!templates || templates.length === 0) {
      await storageManager.saveTemplates(storageManager.getDefaultTemplates());
      console.log('已创建默认模板');
    }

    console.log('扩展初始化完成');
  } catch (error) {
    console.error('扩展初始化失败:', error);
  }
}

// 监听来自content script和popup的消息
browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到消息:', message);

  // 处理异步消息
  const handleMessage = async () => {
    try {
      switch (message.action) {
        case 'generateSummary':
          return await handleGenerateSummary(message.data);
        
        case 'saveApiConfig':
          return await handleSaveApiConfig(message.data);
        
        case 'getApiConfig':
          return await handleGetApiConfig();
        
        case 'testApiConnection':
          return await handleTestApiConnection(message.data);
        
        case 'saveTemplate':
          return await handleSaveTemplate(message.data);
        
        case 'getTemplates':
          return await handleGetTemplates();
        
        case 'deleteTemplate':
          return await handleDeleteTemplate(message.data);
        
        case 'getSummaryHistory':
          return await handleGetSummaryHistory(message.data);
        
        case 'deleteSummaryHistory':
          return await handleDeleteSummaryHistory(message.data);

        case 'deleteSummaryHistoryBatch':
          return await handleDeleteSummaryHistoryBatch(message.data);

        case 'clearAllSummaryHistory':
          return await handleClearAllSummaryHistory();

        case 'saveSettings':
          return await handleSaveSettings(message.data);
        
        case 'getSettings':
          return await handleGetSettings();
        
        case 'exportData':
          return await handleExportData();
        
        case 'importData':
          return await handleImportData(message.data);
        
        default:
          console.warn('未知的消息类型:', message.action);
          return { success: false, error: '未知的消息类型' };
      }
    } catch (error) {
      console.error('处理消息时出错:', error);
      return { success: false, error: error.message };
    }
  };

  // 执行异步处理并发送响应
  handleMessage().then(sendResponse).catch(error => {
    console.error('消息处理异常:', error);
    sendResponse({ success: false, error: error.message });
  });

  // 返回true表示异步响应
  return true;
});

/**
 * 处理生成总结请求
 * @param {Object} data - 请求数据
 * @returns {Promise<Object>} 处理结果
 */
async function handleGenerateSummary(data) {
  const { content, template, provider, videoId, videoTitle } = data;
  
  // 验证输入数据
  if (!content || !template) {
    return { success: false, error: '缺少必要的参数' };
  }

  // 验证内容
  const validation = apiHandler.validateContent(content);
  if (!validation.valid) {
    return { success: false, error: validation.error };
  }

  // 生成总结
  const result = await apiHandler.generateSummary(content, template, provider);
  
  // 如果成功，保存到历史记录
  if (result.success) {
    const summaryId = await storageManager.saveSummaryHistory({
      videoId: videoId,
      videoTitle: videoTitle,
      summary: result.summary,
      template: template,
      provider: result.provider
    });
    
    result.summaryId = summaryId;
  }

  return result;
}

/**
 * 处理保存API配置请求
 * @param {Object} data - API配置数据
 * @returns {Promise<Object>} 处理结果
 */
async function handleSaveApiConfig(data) {
  const success = await storageManager.saveApiConfig(data);
  return { success, message: success ? 'API配置已保存' : '保存API配置失败' };
}

/**
 * 处理获取API配置请求
 * @returns {Promise<Object>} API配置
 */
async function handleGetApiConfig() {
  const config = await storageManager.getApiConfig();
  return { success: true, data: config };
}

/**
 * 处理测试API连接请求
 * @param {Object} data - 测试数据
 * @returns {Promise<Object>} 测试结果
 */
async function handleTestApiConnection(data) {
  const { provider } = data;
  const result = await apiHandler.testApiConnection(provider);
  return result;
}

/**
 * 处理保存模板请求
 * @param {Object} data - 模板数据
 * @returns {Promise<Object>} 处理结果
 */
async function handleSaveTemplate(data) {
  const templates = await storageManager.getTemplates();
  
  if (data.id) {
    // 更新现有模板
    const index = templates.findIndex(t => t.id === data.id);
    if (index !== -1) {
      templates[index] = { ...templates[index], ...data };
    } else {
      return { success: false, error: '模板不存在' };
    }
  } else {
    // 添加新模板
    const newTemplate = {
      id: storageManager.generateId(),
      name: data.name,
      template: data.template,
      isDefault: false,
      created: Date.now()
    };
    templates.push(newTemplate);
  }

  const success = await storageManager.saveTemplates(templates);
  return { success, message: success ? '模板已保存' : '保存模板失败' };
}

/**
 * 处理获取模板请求
 * @returns {Promise<Object>} 模板列表
 */
async function handleGetTemplates() {
  const templates = await storageManager.getTemplates();
  return { success: true, data: templates };
}

/**
 * 处理删除模板请求
 * @param {Object} data - 删除数据
 * @returns {Promise<Object>} 处理结果
 */
async function handleDeleteTemplate(data) {
  const { templateId } = data;
  const templates = await storageManager.getTemplates();
  
  // 不能删除默认模板
  const template = templates.find(t => t.id === templateId);
  if (template && template.isDefault) {
    return { success: false, error: '不能删除默认模板' };
  }

  const filteredTemplates = templates.filter(t => t.id !== templateId);
  const success = await storageManager.saveTemplates(filteredTemplates);
  
  return { success, message: success ? '模板已删除' : '删除模板失败' };
}

/**
 * 处理获取总结历史请求
 * @param {Object} data - 请求数据
 * @returns {Promise<Object>} 历史记录
 */
async function handleGetSummaryHistory(data) {
  const { limit } = data || {};
  const history = await storageManager.getSummaryHistory(limit);
  return { success: true, data: history };
}

/**
 * 处理删除总结历史请求
 * @param {Object} data - 删除数据
 * @returns {Promise<Object>} 处理结果
 */
async function handleDeleteSummaryHistory(data) {
  const { summaryId } = data;
  const success = await storageManager.deleteSummaryHistory(summaryId);
  return { success, message: success ? '历史记录已删除' : '删除历史记录失败' };
}

/**
 * 处理批量删除总结历史请求
 * @param {Object} data - 删除数据
 * @returns {Promise<Object>} 处理结果
 */
async function handleDeleteSummaryHistoryBatch(data) {
  const { summaryIds } = data;
  if (!Array.isArray(summaryIds) || summaryIds.length === 0) {
    return { success: false, message: '无效的删除请求' };
  }

  const success = await storageManager.deleteSummaryHistoryBatch(summaryIds);
  return {
    success,
    message: success ? `已删除 ${summaryIds.length} 条历史记录` : '批量删除失败'
  };
}

/**
 * 处理清空所有总结历史请求
 * @returns {Promise<Object>} 处理结果
 */
async function handleClearAllSummaryHistory() {
  const success = await storageManager.clearAllSummaryHistory();
  return { success, message: success ? '所有历史记录已清空' : '清空历史记录失败' };
}

/**
 * 处理保存设置请求
 * @param {Object} data - 设置数据
 * @returns {Promise<Object>} 处理结果
 */
async function handleSaveSettings(data) {
  const success = await storageManager.saveSettings(data);
  return { success, message: success ? '设置已保存' : '保存设置失败' };
}

/**
 * 处理获取设置请求
 * @returns {Promise<Object>} 设置数据
 */
async function handleGetSettings() {
  const settings = await storageManager.getSettings();
  return { success: true, data: settings };
}

/**
 * 处理导出数据请求
 * @returns {Promise<Object>} 导出的数据
 */
async function handleExportData() {
  try {
    const [config, templates, history, settings] = await Promise.all([
      storageManager.getApiConfig(),
      storageManager.getTemplates(),
      storageManager.getSummaryHistory(100),
      storageManager.getSettings()
    ]);

    const exportData = {
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      data: {
        // 不导出API密钥，只导出提供商信息
        apiConfig: config ? { provider: config.provider, baseUrl: config.baseUrl } : null,
        templates: templates,
        summaryHistory: history,
        settings: settings
      }
    };

    return { success: true, data: exportData };
  } catch (error) {
    return { success: false, error: '导出数据失败: ' + error.message };
  }
}

/**
 * 处理导入数据请求
 * @param {Object} data - 导入的数据
 * @returns {Promise<Object>} 处理结果
 */
async function handleImportData(data) {
  try {
    const { importData } = data;
    
    if (!importData || !importData.data) {
      return { success: false, error: '导入数据格式错误' };
    }

    const { templates, summaryHistory, settings } = importData.data;

    // 导入模板（合并，不覆盖）
    if (templates && Array.isArray(templates)) {
      const existingTemplates = await storageManager.getTemplates();
      const mergedTemplates = [...existingTemplates];
      
      templates.forEach(template => {
        if (!template.isDefault && !existingTemplates.find(t => t.id === template.id)) {
          mergedTemplates.push(template);
        }
      });
      
      await storageManager.saveTemplates(mergedTemplates);
    }

    // 导入历史记录（合并）
    if (summaryHistory && Array.isArray(summaryHistory)) {
      const existingHistory = await storageManager.getSummaryHistory(100);
      const mergedHistory = [...summaryHistory, ...existingHistory];
      
      // 去重并限制数量
      const uniqueHistory = mergedHistory.filter((item, index, arr) => 
        arr.findIndex(h => h.id === item.id) === index
      ).slice(0, 100);
      
      await storageManager.storage.set({ summaryHistory: uniqueHistory });
    }

    // 导入设置（选择性覆盖）
    if (settings) {
      const currentSettings = await storageManager.getSettings();
      const mergedSettings = { ...currentSettings, ...settings };
      await storageManager.saveSettings(mergedSettings);
    }

    return { success: true, message: '数据导入成功' };
  } catch (error) {
    return { success: false, error: '导入数据失败: ' + error.message };
  }
}

// 监听标签页更新，检测YouTube页面
browser.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('youtube.com/watch')) {
    // 检查tabId是否有效
    if (!tabId || tabId < 0) {
      console.log('无效的tabId:', tabId);
      return;
    }
    
    // 等待一段时间确保content script已加载
    setTimeout(async () => {
      try {
        // 向content script发送页面加载完成的消息
        await browser.tabs.sendMessage(tabId, {
          action: 'pageLoaded',
          url: tab.url
        });
        console.log('已向标签页发送pageLoaded消息:', tabId);
      } catch (error) {
        // 忽略无法发送消息的错误（可能content script还未加载）
        console.log('无法向标签页发送消息:', error.message);
      }
    }, 2000); // 等待2秒确保content script加载完成
  }
});



console.log('YouTube总结器后台脚本已加载');