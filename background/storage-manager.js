/**
 * 数据存储管理模块
 * 负责API密钥、用户设置和总结历史的安全存储
 */

class StorageManager {
  constructor() {
    this.storage = browser.storage.local;
  }

  /**
   * 保存API配置
   * @param {Object} config - API配置对象
   * @param {string} config.provider - API提供商 (deepseek, openai)
   * @param {string} config.apiKey - API密钥
   * @param {string} config.baseUrl - API基础URL
   */
  async saveApiConfig(config) {
    try {
      // 加密存储API密钥
      const encryptedConfig = {
        provider: config.provider,
        apiKey: this.encryptApiKey(config.apiKey),
        baseUrl: config.baseUrl || this.getDefaultBaseUrl(config.provider),
        timestamp: Date.now()
      };
      
      await this.storage.set({ apiConfig: encryptedConfig });
      console.log('API配置已保存');
      return true;
    } catch (error) {
      console.error('保存API配置失败:', error);
      return false;
    }
  }

  /**
   * 获取API配置
   * @returns {Object|null} 解密后的API配置
   */
  async getApiConfig() {
    try {
      const result = await this.storage.get('apiConfig');
      if (!result.apiConfig) {
        return null;
      }

      // 解密API密钥
      const config = result.apiConfig;
      return {
        provider: config.provider,
        apiKey: this.decryptApiKey(config.apiKey),
        baseUrl: config.baseUrl,
        timestamp: config.timestamp
      };
    } catch (error) {
      console.error('获取API配置失败:', error);
      return null;
    }
  }

  /**
   * 保存提示模板
   * @param {Array} templates - 模板数组
   */
  async saveTemplates(templates) {
    try {
      await this.storage.set({ 
        promptTemplates: templates,
        templatesUpdated: Date.now()
      });
      console.log('提示模板已保存');
      return true;
    } catch (error) {
      console.error('保存提示模板失败:', error);
      return false;
    }
  }

  /**
   * 获取提示模板
   * @returns {Array} 模板数组
   */
  async getTemplates() {
    try {
      const result = await this.storage.get('promptTemplates');
      return result.promptTemplates || this.getDefaultTemplates();
    } catch (error) {
      console.error('获取提示模板失败:', error);
      return this.getDefaultTemplates();
    }
  }

  /**
   * 保存总结历史
   * @param {Object} summary - 总结对象
   */
  async saveSummaryHistory(summary) {
    try {
      const result = await this.storage.get('summaryHistory');
      const history = result.summaryHistory || [];
      
      // 添加新总结到历史记录
      const newSummary = {
        id: this.generateId(),
        videoId: summary.videoId,
        videoTitle: summary.videoTitle,
        summary: summary.summary,
        template: summary.template,
        provider: summary.provider,
        timestamp: Date.now()
      };
      
      history.unshift(newSummary);
      
      // 限制历史记录数量 (最多保存100条)
      if (history.length > 100) {
        history.splice(100);
      }
      
      await this.storage.set({ summaryHistory: history });
      console.log('总结历史已保存');
      return newSummary.id;
    } catch (error) {
      console.error('保存总结历史失败:', error);
      return null;
    }
  }

  /**
   * 获取总结历史
   * @param {number} limit - 限制返回数量
   * @returns {Array} 历史记录数组
   */
  async getSummaryHistory(limit = 20) {
    try {
      const result = await this.storage.get('summaryHistory');
      const history = result.summaryHistory || [];
      return history.slice(0, limit);
    } catch (error) {
      console.error('获取总结历史失败:', error);
      return [];
    }
  }

  /**
   * 删除总结历史记录
   * @param {string} summaryId - 总结ID
   */
  async deleteSummaryHistory(summaryId) {
    try {
      const result = await this.storage.get('summaryHistory');
      const history = result.summaryHistory || [];
      const filteredHistory = history.filter(item => item.id !== summaryId);
      
      await this.storage.set({ summaryHistory: filteredHistory });
      console.log('总结历史已删除');
      return true;
    } catch (error) {
      console.error('删除总结历史失败:', error);
      return false;
    }
  }

  /**
   * 保存用户设置
   * @param {Object} settings - 设置对象
   */
  async saveSettings(settings) {
    try {
      await this.storage.set({ 
        userSettings: settings,
        settingsUpdated: Date.now()
      });
      console.log('用户设置已保存');
      return true;
    } catch (error) {
      console.error('保存用户设置失败:', error);
      return false;
    }
  }

  /**
   * 获取用户设置
   * @returns {Object} 设置对象
   */
  async getSettings() {
    try {
      const result = await this.storage.get('userSettings');
      return result.userSettings || this.getDefaultSettings();
    } catch (error) {
      console.error('获取用户设置失败:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * 简单的API密钥加密 (基础混淆，非强加密)
   * @param {string} apiKey - 原始API密钥
   * @returns {string} 加密后的密钥
   */
  encryptApiKey(apiKey) {
    if (!apiKey) return '';
    // 简单的Base64编码 + 字符偏移
    const encoded = btoa(apiKey);
    return encoded.split('').map(char => 
      String.fromCharCode(char.charCodeAt(0) + 3)
    ).join('');
  }

  /**
   * 解密API密钥
   * @param {string} encryptedKey - 加密的密钥
   * @returns {string} 解密后的密钥
   */
  decryptApiKey(encryptedKey) {
    if (!encryptedKey) return '';
    try {
      // 逆向解密过程
      const decoded = encryptedKey.split('').map(char => 
        String.fromCharCode(char.charCodeAt(0) - 3)
      ).join('');
      return atob(decoded);
    } catch (error) {
      console.error('解密API密钥失败:', error);
      return '';
    }
  }

  /**
   * 获取默认API基础URL
   * @param {string} provider - API提供商
   * @returns {string} 基础URL
   */
  getDefaultBaseUrl(provider) {
    const urls = {
      deepseek: 'https://api.deepseek.com',
      openai: 'https://api.openai.com'
    };
    return urls[provider] || '';
  }

  /**
   * 获取默认提示模板
   * @returns {Array} 默认模板数组
   */
  getDefaultTemplates() {
    return [
      {
        id: 'basic',
        name: '基础总结',
        template: `请对以下YouTube视频内容进行总结：

🎯 核心主题：[视频的主要话题和中心观点]

📝 关键要点：
1. [要点1]
2. [要点2] 
3. [要点3]

💡 主要论点：
• [论点1及其支撑证据]
• [论点2及其支撑证据]

🔍 深度分析：[对内容的详细解读和分析]

✨ 学习价值：[视频的教育意义和实用价值]

内容：{content}`,
        isDefault: true
      },
      {
        id: 'concise',
        name: '简洁总结',
        template: `请用简洁的方式总结以下YouTube视频内容，重点突出核心观点和关键信息：

内容：{content}`,
        isDefault: true
      },
      {
        id: 'detailed',
        name: '详细分析',
        template: `请对以下YouTube视频内容进行详细分析：

📋 内容概述：[视频整体内容的简要概述]

🎯 核心观点：
1. [核心观点1 - 详细说明]
2. [核心观点2 - 详细说明]
3. [核心观点3 - 详细说明]

📊 论证结构：
• 前提假设：[视频基于的前提或假设]
• 论证过程：[如何展开论证]
• 结论要点：[得出的主要结论]

🔗 相关联系：[与其他知识点或现实情况的联系]

💭 个人思考：[值得深入思考的问题或启发]

内容：{content}`,
        isDefault: true
      }
    ];
  }

  /**
   * 获取默认用户设置
   * @returns {Object} 默认设置
   */
  getDefaultSettings() {
    return {
      defaultProvider: 'deepseek',
      defaultTemplate: 'basic',
      autoSummarize: false,
      showHistory: true,
      maxHistoryItems: 50,
      language: 'zh-CN'
    };
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一标识符
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 清除所有数据 (用于重置)
   */
  async clearAllData() {
    try {
      await this.storage.clear();
      console.log('所有数据已清除');
      return true;
    } catch (error) {
      console.error('清除数据失败:', error);
      return false;
    }
  }
}

// 导出单例实例
const storageManager = new StorageManager();