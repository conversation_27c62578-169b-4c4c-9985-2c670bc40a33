/**
 * AI API处理模块
 * 负责与各种AI服务提供商的API通信
 */

class ApiHandler {
  constructor() {
    this.supportedProviders = ['deepseek', 'openai'];
    this.requestTimeout = 30000; // 30秒超时
  }

  /**
   * 生成总结
   * @param {string} content - 视频字幕内容
   * @param {string} template - 提示模板
   * @param {string} provider - API提供商
   * @returns {Promise<Object>} 总结结果
   */
  async generateSummary(content, template, provider = 'deepseek') {
    try {
      // 获取API配置
      const config = await storageManager.getApiConfig();
      if (!config) {
        throw new Error('未找到API配置，请先在设置中配置API密钥');
      }

      if (config.provider !== provider) {
        throw new Error(`当前配置的提供商是${config.provider}，但请求的是${provider}，请检查设置`);
      }

      // 验证API密钥
      if (!config.apiKey || config.apiKey.trim().length === 0) {
        throw new Error(`${provider}的API密钥为空，请在设置中配置正确的API密钥`);
      }

      // 验证基础URL
      if (!config.baseUrl || !config.baseUrl.startsWith('http')) {
        throw new Error(`${provider}的API基础URL无效: ${config.baseUrl}`);
      }

      // 验证内容长度
      if (!content || content.trim().length < 50) {
        throw new Error('视频内容太短，无法生成有效总结。请确保视频有字幕且内容足够长');
      }

      console.log(`开始生成总结 - 提供商: ${provider}, 内容长度: ${content.length}字符`);

      // 构建提示词
      const prompt = this.buildPrompt(template, content);
      
      // 根据提供商调用相应的API
      let result;
      switch (provider) {
        case 'deepseek':
          result = await this.callDeepSeekAPI(config, prompt);
          break;
        case 'openai':
          result = await this.callOpenAIAPI(config, prompt);
          break;
        default:
          throw new Error(`不支持的API提供商: ${provider}`);
      }

      return {
        success: true,
        summary: result.summary,
        provider: provider,
        model: result.model,
        usage: result.usage,
        timestamp: Date.now()
      };

    } catch (error) {
      console.error('生成总结失败:', error);
      return {
        success: false,
        error: error.message,
        provider: provider,
        timestamp: Date.now()
      };
    }
  }

  /**
   * 调用DeepSeek API
   * @param {Object} config - API配置
   * @param {string} prompt - 提示词
   * @returns {Promise<Object>} API响应
   */
  async callDeepSeekAPI(config, prompt) {
    const url = `${config.baseUrl}/v1/chat/completions`;
    
    const requestBody = {
      model: 'deepseek-chat',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的视频内容分析师，擅长提取关键信息并生成结构化总结。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.7,
      stream: false
    };

    const response = await this.makeRequest(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.choices || response.choices.length === 0) {
      throw new Error('DeepSeek API返回空响应');
    }

    return {
      summary: response.choices[0].message.content,
      model: response.model,
      usage: response.usage
    };
  }

  /**
   * 调用OpenAI API
   * @param {Object} config - API配置
   * @param {string} prompt - 提示词
   * @returns {Promise<Object>} API响应
   */
  async callOpenAIAPI(config, prompt) {
    const url = `${config.baseUrl}/v1/chat/completions`;
    
    const requestBody = {
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的视频内容分析师，擅长提取关键信息并生成结构化总结。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.7
    };

    const response = await this.makeRequest(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.choices || response.choices.length === 0) {
      throw new Error('OpenAI API返回空响应');
    }

    return {
      summary: response.choices[0].message.content,
      model: response.model,
      usage: response.usage
    };
  }

  

  /**
   * 构建提示词
   * @param {string} template - 模板
   * @param {string} content - 内容
   * @returns {string} 完整的提示词
   */
  buildPrompt(template, content) {
    // 限制内容长度，避免超出API限制
    const maxContentLength = 8000; // 约2000个token
    let processedContent = content;
    
    if (content.length > maxContentLength) {
      // 智能截取：保留开头和结尾部分
      const startPart = content.substring(0, maxContentLength * 0.6);
      const endPart = content.substring(content.length - maxContentLength * 0.3);
      processedContent = startPart + '\n\n[... 中间部分已省略 ...]\n\n' + endPart;
    }

    // 替换模板中的占位符
    return template.replace('{content}', processedContent);
  }

  /**
   * 发送HTTP请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async makeRequest(url, options) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      return data;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }
      
      throw error;
    }
  }

  /**
   * 测试API连接
   * @param {string} provider - API提供商
   * @returns {Promise<Object>} 测试结果
   */
  async testApiConnection(provider) {
    try {
      const config = await storageManager.getApiConfig();
      if (!config || config.provider !== provider) {
        return {
          success: false,
          error: `未找到${provider}的API配置`
        };
      }

      // 发送简单的测试请求
      const testPrompt = '请回复"连接测试成功"';
      const result = await this.generateSummary('这是一个测试内容', testPrompt, provider);
      
      return {
        success: result.success,
        message: result.success ? 'API连接正常' : result.error,
        provider: provider
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        provider: provider
      };
    }
  }

  /**
   * 获取支持的模型列表
   * @param {string} provider - API提供商
   * @returns {Array} 模型列表
   */
  getSupportedModels(provider) {
    const models = {
      deepseek: [
        { id: 'deepseek-chat', name: 'DeepSeek Chat', description: '通用对话模型' },
        { id: 'deepseek-coder', name: 'DeepSeek Coder', description: '代码专用模型' }
      ],
      openai: [
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: '快速响应模型' },
        { id: 'gpt-4', name: 'GPT-4', description: '高质量模型' },
        { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: '最新优化模型' }
      ]
    };

    return models[provider] || [];
  }

  /**
   * 估算token使用量
   * @param {string} text - 文本内容
   * @returns {number} 估算的token数量
   */
  estimateTokens(text) {
    // 简单估算：中文约1.5字符/token，英文约4字符/token
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const otherChars = text.length - chineseChars;
    
    return Math.ceil(chineseChars / 1.5 + otherChars / 4);
  }

  /**
   * 检查内容是否适合处理
   * @param {string} content - 内容
   * @returns {Object} 检查结果
   */
  validateContent(content) {
    if (!content || typeof content !== 'string') {
      return {
        valid: false,
        error: '内容不能为空'
      };
    }

    const trimmedContent = content.trim();
    if (trimmedContent.length < 50) {
      return {
        valid: false,
        error: '内容太短，至少需要50个字符'
      };
    }

    const estimatedTokens = this.estimateTokens(trimmedContent);
    if (estimatedTokens > 10000) {
      return {
        valid: false,
        error: '内容太长，请选择较短的视频'
      };
    }

    return {
      valid: true,
      length: trimmedContent.length,
      estimatedTokens: estimatedTokens
    };
  }
}

// 导出单例实例
const apiHandler = new ApiHandler();