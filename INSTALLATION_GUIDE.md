# YouTube总结器 Firefox扩展 - 安装和使用指南

## 📦 安装步骤

### 1. 下载扩展文件
- 下载 `youtube-summarizer-v1.0.3-enhanced.xpi` 文件

### 2. 在Firefox中安装
1. 打开Firefox浏览器
2. 按 `Ctrl+Shift+A` 打开附加组件管理器
3. 点击右上角的齿轮图标 ⚙️
4. 选择"从文件安装附加组件"
5. 选择下载的 `.xpi` 文件
6. 点击"添加"确认安装

### 3. 验证安装
- 在Firefox工具栏中应该能看到YouTube总结器图标
- 点击图标应该能打开扩展弹窗

## ⚙️ 配置设置

### 1. 配置AI API
1. 点击扩展图标，然后点击"打开设置"
2. 选择AI服务提供商（DeepSeek或OpenAI）
3. 输入对应的API密钥
4. 点击"测试连接"验证配置
5. 保存设置

### 2. API密钥获取
- **DeepSeek**: 访问 [https://platform.deepseek.com](https://platform.deepseek.com) 注册并获取API密钥
- **OpenAI**: 访问 [https://platform.openai.com](https://platform.openai.com) 注册并获取API密钥

### 3. 自定义模板（可选）
- 在设置页面可以创建和编辑总结模板
- 支持使用 `{content}` 占位符插入视频内容

## 🎯 使用方法

### 方法1: 使用扩展图标
1. 在YouTube视频页面点击扩展图标
2. 确认显示"已检测到YouTube视频页面"
3. 点击"生成总结"按钮

### 方法2: 使用页面按钮
1. 在YouTube视频页面查找"AI总结"按钮
2. 直接点击按钮开始生成总结

### 方法3: 使用快捷键
- `Ctrl+Shift+S`: 触发总结生成
- `Ctrl+Shift+D`: 运行诊断工具
- `Ctrl+Shift+C`: 清除缓存

## 🔍 问题诊断

### 自动诊断工具
按 `Ctrl+Shift+D` 运行诊断工具，它会检查：
- 浏览器和页面状态
- 扩展依赖加载情况
- YouTube功能可用性
- API配置状态
- 存储和权限状态

### 常见问题解决

#### 1. 点击按钮没有反应
**可能原因**:
- 扩展依赖未完全加载
- YouTube页面结构变化
- API配置问题

**解决方法**:
1. 刷新页面重试
2. 运行诊断工具检查状态
3. 检查浏览器控制台错误信息

#### 2. 字幕提取失败
**可能原因**:
- 视频没有字幕
- 字幕未启用
- YouTube结构变化

**解决方法**:
1. 确认视频有可用字幕
2. 手动启用字幕后重试
3. 尝试其他有字幕的视频

#### 3. API调用失败
**可能原因**:
- API密钥无效或过期
- 网络连接问题
- API服务暂时不可用

**解决方法**:
1. 检查API密钥配置
2. 测试API连接
3. 检查网络连接
4. 稍后重试

#### 4. 扩展图标点击无反应
**可能原因**:
- 扩展未正确安装
- Firefox版本不兼容

**解决方法**:
1. 重新安装扩展
2. 检查Firefox版本（需要88+）
3. 重启浏览器

## 📊 功能特性

### 核心功能
- ✅ 自动提取YouTube视频字幕
- ✅ AI智能总结生成
- ✅ 多种总结模板
- ✅ 历史记录管理
- ✅ 数据导出/导入

### 高级功能
- ✅ 自定义提示模板
- ✅ 多AI服务支持
- ✅ 实时状态监控
- ✅ 智能错误诊断
- ✅ 快捷键操作

### 安全特性
- ✅ 本地数据存储
- ✅ API密钥加密
- ✅ 最小权限原则
- ✅ 隐私保护

## 🛠️ 高级设置

### 调试模式
1. 打开Firefox开发者工具（F12）
2. 查看控制台输出
3. 运行诊断工具获取详细信息

### 数据管理
- **导出数据**: 在设置页面可以导出配置和历史
- **导入数据**: 可以导入之前导出的数据
- **清除数据**: 可以清除所有存储的数据

### 性能优化
- 扩展会自动缓存字幕数据
- 支持多种字幕提取方法
- 智能重试机制提高成功率

## 📞 技术支持

### 获取帮助
1. 首先运行诊断工具（Ctrl+Shift+D）
2. 查看浏览器控制台错误信息
3. 参考本指南的常见问题部分

### 报告问题
提供以下信息有助于问题解决：
- Firefox版本
- 扩展版本
- 诊断工具输出
- 具体错误信息
- 复现步骤

### 版本兼容性
- **Firefox**: 88.0 或更高版本
- **操作系统**: Windows, macOS, Linux
- **YouTube**: 支持当前所有版本

---

**版本**: v1.0.3  
**更新日期**: 2025年6月23日  
**维护状态**: 积极维护中
