# YouTube总结器 Firefox扩展

一个强大的Firefox扩展，能够自动提取YouTube视频字幕并使用AI生成智能总结。

## 功能特性

### 🎯 核心功能
- ✅ 自动提取YouTube视频字幕
- ✅ 支持多种AI模型生成总结 (DeepSeek, OpenAI, Ollama)
- ✅ 可自定义提示模板
- ✅ 总结历史记录管理
- ✅ 多语言字幕支持
- ✅ 安全的本地数据存储

### 🚀 用户体验
- 一键生成视频总结
- 工具栏按钮 + 模态窗口显示
- 响应式设计，支持移动端
- 深色模式自动适配
- 快捷键支持 (Ctrl+Shift+S)

### 🔧 技术特性
- Firefox WebExtensions API (Manifest V2)
- 原生JavaScript，无外部依赖
- 安全的API密钥加密存储
- 完善的错误处理机制

## 安装方法

### 方法1: 开发者模式安装
1. 下载项目源码
2. 打开Firefox，进入 `about:debugging`
3. 点击"此Firefox"
4. 点击"临时载入附加组件"
5. 选择项目中的 `manifest.json` 文件

### 方法2: 打包安装
1. 将项目打包为 `.xpi` 文件
2. 在Firefox中打开 `.xpi` 文件进行安装

## 使用指南

### 1. 配置API
1. 点击扩展图标或右键选择"选项"
2. 在"API配置"标签页中选择AI提供商
3. 输入相应的API密钥
4. 点击"测试连接"确认配置正确

### 2. 生成总结
1. 打开任意YouTube视频页面
2. 点击视频下方的"AI总结"按钮
3. 等待字幕提取和总结生成
4. 在弹出窗口中查看总结结果

### 3. 管理模板
1. 在设置页面的"提示模板"标签页
2. 可以添加、编辑、删除自定义模板
3. 模板中使用 `{content}` 作为视频内容占位符

## API配置指南

### DeepSeek (推荐)
- 成本低，质量高
- 访问: https://platform.deepseek.com
- 注册账号并创建API密钥

### OpenAI
- 质量最高，成本较高
- 访问: https://platform.openai.com
- 需要有效的API密钥和余额

### Ollama (本地)
- 完全免费，隐私保护
- 下载: https://ollama.ai
- 本地运行，需要下载模型

## 快捷键

- `Ctrl + Shift + S` - 生成总结
- `Ctrl + Shift + C` - 清除缓存

## 项目结构

```
youtube-summarizer/
├── manifest.json              # 扩展配置文件
├── background/               # 后台脚本
│   ├── background.js        # 主后台脚本
│   ├── api-handler.js       # AI API处理
│   └── storage-manager.js   # 数据存储管理
├── content/                 # 内容脚本
│   ├── content.js          # 主内容脚本
│   ├── youtube-injector.js # YouTube页面注入
│   └── transcript-extractor.js # 字幕提取
├── settings/               # 设置页面
│   ├── settings.html       # 设置页面HTML
│   ├── settings.css        # 设置页面样式
│   └── settings.js         # 设置页面逻辑
├── libs/                   # 第三方库
│   └── youtube-transcript-api.js # 字幕提取库
├── assets/                 # 资源文件
│   ├── icons/             # 扩展图标
│   └── styles/            # 样式文件
└── README.md              # 项目说明
```

## 开发说明

### 技术栈
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **扩展API**: Firefox WebExtensions API
- **AI集成**: REST API调用
- **数据存储**: Firefox Storage API

### 开发环境
1. 安装Firefox开发者版本
2. 启用扩展调试模式
3. 使用浏览器开发者工具调试

### 构建和打包
```bash
# 创建扩展包
zip -r youtube-summarizer.xpi * -x "*.git*" "*.DS_Store*" "node_modules/*"
```

## 隐私和安全

### 数据处理
- 所有数据本地存储，不上传到第三方服务器
- API密钥经过加密存储
- 视频内容仅用于生成总结，不做其他用途

### 权限说明
- `activeTab`: 访问当前活动标签页
- `storage`: 本地数据存储
- `https://www.youtube.com/*`: 访问YouTube页面
- `https://api.deepseek.com/*`: 调用DeepSeek API
- `https://api.openai.com/*`: 调用OpenAI API
- `http://localhost:*`: 访问本地Ollama服务

## 故障排除

### 常见问题

**Q: 按钮没有出现在YouTube页面**
A: 刷新页面，或检查扩展是否正确安装和启用

**Q: 提示"无法获取字幕"**
A: 确认视频有可用字幕，或尝试其他视频

**Q: API调用失败**
A: 检查API密钥是否正确，网络连接是否正常

**Q: 总结质量不佳**
A: 尝试调整提示模板，或更换AI提供商

### 调试方法
1. 打开浏览器开发者工具
2. 查看控制台错误信息
3. 检查网络请求状态
4. 验证扩展权限设置

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持DeepSeek, OpenAI, Ollama
- 基础总结功能
- 设置页面和历史记录

## 贡献指南

欢迎提交问题报告和功能建议！

### 开发贡献
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

### 问题反馈
- 🐛 Bug报告
- 💡 功能建议
- 📝 文档改进
- 🌐 多语言支持

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 致谢

- YouTube Transcript API
- Firefox WebExtensions API
- 各AI服务提供商
- 开源社区的支持

---

**注意**: 本扩展仅供学习和个人使用，请遵守相关服务的使用条款。