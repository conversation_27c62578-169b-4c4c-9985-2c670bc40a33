<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF0000;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CC0000;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#990000" stroke-width="2"/>
  
  <!-- YouTube播放按钮风格的三角形 -->
  <polygon points="50,45 50,83 85,64" fill="white"/>
  
  <!-- AI总结图标 - 文档线条 -->
  <g transform="translate(75, 35)">
    <rect x="0" y="0" width="20" height="25" rx="2" fill="white" opacity="0.9"/>
    <line x1="3" y1="5" x2="17" y2="5" stroke="#FF0000" stroke-width="1.5"/>
    <line x1="3" y1="9" x2="17" y2="9" stroke="#FF0000" stroke-width="1.5"/>
    <line x1="3" y1="13" x2="14" y2="13" stroke="#FF0000" stroke-width="1.5"/>
    <line x1="3" y1="17" x2="12" y2="17" stroke="#FF0000" stroke-width="1.5"/>
  </g>
  
  <!-- AI标识 -->
  <text x="64" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">AI总结</text>
</svg>