<!DOCTYPE html>
<html>
<head>
    <title>图标生成器</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-set { margin: 20px 0; }
    </style>
</head>
<body>
    <h1>YouTube总结器图标生成器</h1>
    <p>此页面用于生成扩展所需的PNG图标文件</p>
    
    <div class="icon-set">
        <h3>16x16 图标</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
        <button onclick="downloadIcon('icon16', 'icon-16.png')">下载</button>
    </div>
    
    <div class="icon-set">
        <h3>48x48 图标</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
        <button onclick="downloadIcon('icon48', 'icon-48.png')">下载</button>
    </div>
    
    <div class="icon-set">
        <h3>128x128 图标</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
        <button onclick="downloadIcon('icon128', 'icon-128.png')">下载</button>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景圆形
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 4*scale, 0, 2 * Math.PI);
            ctx.fillStyle = '#065fd4';
            ctx.fill();
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 4 * scale;
            ctx.stroke();
            
            // 文档形状
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(40*scale, 30*scale, 48*scale, 68*scale);
            
            // 文档折角
            ctx.beginPath();
            ctx.moveTo(73*scale, 30*scale);
            ctx.lineTo(73*scale, 45*scale);
            ctx.lineTo(88*scale, 45*scale);
            ctx.strokeStyle = '#065fd4';
            ctx.lineWidth = 2 * scale;
            ctx.stroke();
            
            // AI点
            ctx.fillStyle = '#065fd4';
            ctx.beginPath();
            ctx.arc(52*scale, 55*scale, 3*scale, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(64*scale, 55*scale, 3*scale, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(76*scale, 55*scale, 3*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // 文本线条
            ctx.strokeStyle = '#065fd4';
            ctx.lineWidth = 2 * scale;
            ctx.beginPath();
            ctx.moveTo(48*scale, 70*scale);
            ctx.lineTo(80*scale, 70*scale);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(48*scale, 78*scale);
            ctx.lineTo(75*scale, 78*scale);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(48*scale, 86*scale);
            ctx.lineTo(70*scale, 86*scale);
            ctx.stroke();
            
            // YouTube播放按钮
            ctx.fillStyle = '#ff0000';
            ctx.beginPath();
            ctx.arc(64*scale, 40*scale, 8*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // 播放三角形
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.moveTo(60*scale, 36*scale);
            ctx.lineTo(60*scale, 44*scale);
            ctx.lineTo(68*scale, 40*scale);
            ctx.closePath();
            ctx.fill();
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 生成所有图标
        window.onload = function() {
            drawIcon(document.getElementById('icon16'), 16);
            drawIcon(document.getElementById('icon48'), 48);
            drawIcon(document.getElementById('icon128'), 128);
        };
    </script>
</body>
</html>