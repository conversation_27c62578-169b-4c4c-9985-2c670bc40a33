/**
 * 内容脚本样式文件
 * 用于YouTube页面注入的UI元素样式
 */

/* 总结按钮容器样式 */
.yt-summarizer-button-container {
    display: inline-flex !important;
    align-items: center !important;
    margin-left: 8px !important;
    margin-right: 8px !important;
    z-index: 1000 !important;
}

/* 总结按钮样式 */
.yt-summarizer-button {
    display: flex !important;
    align-items: center !important;
    padding: 8px 16px !important;
    background: #065fd4 !important;
    color: white !important;
    border: none !important;
    border-radius: 18px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-family: "YouTube Sans", "Roboto", sans-serif !important;
    text-decoration: none !important;
    outline: none !important;
    box-shadow: none !important;
    min-height: 36px !important;
}

.yt-summarizer-button:hover {
    background: #0b57d0 !important;
    transform: scale(1.02) !important;
    box-shadow: 0 2px 8px rgba(6, 95, 212, 0.3) !important;
}

.yt-summarizer-button:active {
    transform: scale(0.98) !important;
}

.yt-summarizer-button:disabled {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.yt-summarizer-button svg {
    fill: currentColor !important;
    flex-shrink: 0 !important;
}

/* 模态窗口背景 */
.yt-summarizer-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.8) !important;
    z-index: 10000 !important;
    display: none !important;
    justify-content: center !important;
    align-items: center !important;
    backdrop-filter: blur(4px) !important;
    animation: fadeIn 0.3s ease !important;
}

.yt-summarizer-modal-overlay.show {
    display: flex !important;
}

/* 模态窗口内容 */
.yt-summarizer-modal-content {
    background: white !important;
    border-radius: 12px !important;
    width: 90% !important;
    max-width: 800px !important;
    max-height: 80% !important;
    overflow: hidden !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    display: flex !important;
    flex-direction: column !important;
    animation: slideIn 0.3s ease !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* 模态窗口头部 */
.yt-summarizer-modal-header {
    padding: 20px 24px !important;
    border-bottom: 1px solid #e0e0e0 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    background: #f8f9fa !important;
    flex-shrink: 0 !important;
}

.yt-summarizer-modal-header h2 {
    margin: 0 !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    color: #202124 !important;
}

.yt-summarizer-modal-header button {
    background: none !important;
    border: none !important;
    font-size: 24px !important;
    cursor: pointer !important;
    color: #5f6368 !important;
    padding: 4px !important;
    border-radius: 4px !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: background 0.2s ease !important;
}

.yt-summarizer-modal-header button:hover {
    background: #e8eaed !important;
    color: #202124 !important;
}

/* 模态窗口主体 */
.yt-summarizer-modal-body {
    padding: 24px !important;
    overflow-y: auto !important;
    flex: 1 !important;
    min-height: 200px !important;
}

/* 加载状态 */
.yt-summarizer-loading {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 40px !important;
    text-align: center !important;
}

.yt-summarizer-loading div {
    width: 40px !important;
    height: 40px !important;
    border: 4px solid #f3f3f3 !important;
    border-top: 4px solid #065fd4 !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
    margin-bottom: 16px !important;
}

.yt-summarizer-loading p {
    margin: 0 !important;
    color: #5f6368 !important;
    font-size: 16px !important;
}

/* 内容区域 */
.yt-summarizer-content {
    line-height: 1.6 !important;
    color: #202124 !important;
    font-size: 14px !important;
}

.yt-summarizer-content h3 {
    margin: 0 0 12px 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #202124 !important;
}

.yt-summarizer-content p {
    margin: 0 0 12px 0 !important;
}

.yt-summarizer-content pre {
    background: #f8f9fa !important;
    padding: 16px !important;
    border-radius: 8px !important;
    overflow-x: auto !important;
    white-space: pre-wrap !important;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
    border: 1px solid #e0e0e0 !important;
}

/* 状态消息样式 */
.yt-summarizer-status {
    padding: 12px 16px !important;
    border-radius: 8px !important;
    margin-bottom: 16px !important;
    font-size: 14px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.yt-summarizer-status.success {
    background: #e8f5e8 !important;
    color: #137333 !important;
    border: 1px solid #c6e7c6 !important;
}

.yt-summarizer-status.error {
    background: #fce8e6 !important;
    color: #d93025 !important;
    border: 1px solid #f9ab9d !important;
}

.yt-summarizer-status.info {
    background: #e3f2fd !important;
    color: #1976d2 !important;
    border: 1px solid #bbdefb !important;
}

/* 按钮组样式 */
.yt-summarizer-actions {
    margin-top: 24px !important;
    padding-top: 16px !important;
    border-top: 1px solid #e0e0e0 !important;
    display: flex !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
}

.yt-summarizer-actions button {
    padding: 8px 16px !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    outline: none !important;
}

.yt-summarizer-actions .btn-primary {
    background: #1976d2 !important;
    color: white !important;
}

.yt-summarizer-actions .btn-primary:hover {
    background: #1565c0 !important;
}

.yt-summarizer-actions .btn-secondary {
    background: #388e3c !important;
    color: white !important;
}

.yt-summarizer-actions .btn-secondary:hover {
    background: #2e7d32 !important;
}

.yt-summarizer-actions .btn-danger {
    background: #d32f2f !important;
    color: white !important;
}

.yt-summarizer-actions .btn-danger:hover {
    background: #c62828 !important;
}

/* 视频信息样式 */
.yt-summarizer-video-info {
    background: #f8f9fa !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    margin-bottom: 16px !important;
    border-left: 4px solid #1976d2 !important;
}

.yt-summarizer-video-info .title {
    font-weight: 600 !important;
    color: #202124 !important;
    margin-bottom: 4px !important;
}

.yt-summarizer-video-info .meta {
    font-size: 12px !important;
    color: #5f6368 !important;
}

/* 错误页面样式 */
.yt-summarizer-error {
    padding: 20px !important;
    text-align: center !important;
}

.yt-summarizer-error .icon {
    color: #d93025 !important;
    font-size: 48px !important;
    margin-bottom: 16px !important;
}

.yt-summarizer-error h3 {
    color: #d93025 !important;
    margin: 0 0 12px 0 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

.yt-summarizer-error p {
    color: #5f6368 !important;
    margin: 0 0 20px 0 !important;
}

.yt-summarizer-error .actions {
    display: flex !important;
    gap: 8px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
}

.yt-summarizer-error button {
    padding: 8px 16px !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: background 0.2s ease !important;
}

/* 动画定义 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .yt-summarizer-modal-content {
        width: 95% !important;
        max-height: 90% !important;
        margin: 20px !important;
    }
    
    .yt-summarizer-modal-header {
        padding: 16px 20px !important;
    }
    
    .yt-summarizer-modal-body {
        padding: 20px !important;
    }
    
    .yt-summarizer-button {
        padding: 6px 12px !important;
        font-size: 13px !important;
        min-height: 32px !important;
    }
    
    .yt-summarizer-actions {
        flex-direction: column !important;
    }
    
    .yt-summarizer-actions button {
        width: 100% !important;
    }
}

@media (max-width: 480px) {
    .yt-summarizer-modal-content {
        width: 100% !important;
        height: 100% !important;
        max-height: 100% !important;
        border-radius: 0 !important;
        margin: 0 !important;
    }
    
    .yt-summarizer-modal-header {
        padding: 12px 16px !important;
    }
    
    .yt-summarizer-modal-body {
        padding: 16px !important;
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .yt-summarizer-modal-content {
        background: #1f1f1f !important;
        color: #e8eaed !important;
    }
    
    .yt-summarizer-modal-header {
        background: #2d2d2d !important;
        border-bottom-color: #3c4043 !important;
    }
    
    .yt-summarizer-modal-header h2 {
        color: #e8eaed !important;
    }
    
    .yt-summarizer-modal-header button {
        color: #9aa0a6 !important;
    }
    
    .yt-summarizer-modal-header button:hover {
        background: #3c4043 !important;
        color: #e8eaed !important;
    }
    
    .yt-summarizer-content {
        color: #e8eaed !important;
    }
    
    .yt-summarizer-content pre {
        background: #2d2d2d !important;
        border-color: #3c4043 !important;
        color: #e8eaed !important;
    }
    
    .yt-summarizer-video-info {
        background: #2d2d2d !important;
        border-left-color: #8ab4f8 !important;
    }
    
    .yt-summarizer-video-info .title {
        color: #e8eaed !important;
    }
    
    .yt-summarizer-video-info .meta {
        color: #9aa0a6 !important;
    }
    
    .yt-summarizer-status.success {
        background: #1e3a1e !important;
        color: #81c995 !important;
        border-color: #2d5a2d !important;
    }
    
    .yt-summarizer-status.error {
        background: #3d1a1a !important;
        color: #f28b82 !important;
        border-color: #5a2d2d !important;
    }
    
    .yt-summarizer-status.info {
        background: #1a2e3d !important;
        color: #8ab4f8 !important;
        border-color: #2d4a5a !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .yt-summarizer-button {
        border: 2px solid #ffffff !important;
    }
    
    .yt-summarizer-modal-content {
        border: 2px solid #000000 !important;
    }
    
    .yt-summarizer-status {
        border-width: 2px !important;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .yt-summarizer-button,
    .yt-summarizer-modal-overlay,
    .yt-summarizer-modal-content,
    .yt-summarizer-actions button {
        transition: none !important;
        animation: none !important;
    }
    
    .yt-summarizer-loading div {
        animation: none !important;
        border: 4px solid #065fd4 !important;
    }
}

/* 确保样式优先级 */
.yt-summarizer-button-container,
.yt-summarizer-button,
.yt-summarizer-modal-overlay,
.yt-summarizer-modal-content,
.yt-summarizer-modal-header,
.yt-summarizer-modal-body,
.yt-summarizer-loading,
.yt-summarizer-content,
.yt-summarizer-status,
.yt-summarizer-actions,
.yt-summarizer-video-info,
.yt-summarizer-error {
    all: unset !important;
}

/* 重新应用必要的样式 */
.yt-summarizer-button-container {
    display: inline-flex !important;
    align-items: center !important;
    margin-left: 8px !important;
    margin-right: 8px !important;
    z-index: 1000 !important;
}