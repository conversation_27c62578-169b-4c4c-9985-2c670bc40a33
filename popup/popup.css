/* Popup样式 */
body {
    margin: 0;
    padding: 0;
    width: 320px;
    min-height: 200px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #333333;
}

.popup-container {
    padding: 16px;
}

.header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e0e0e0;
}

.logo {
    width: 24px;
    height: 24px;
}

.header h1 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}

.content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.status-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-icon {
    font-size: 16px;
}

.status-text {
    font-size: 14px;
    color: #666666;
}

.status-item.youtube-detected .status-icon {
    color: #ff0000;
}

.status-item.youtube-detected .status-text {
    color: #333333;
    font-weight: 500;
}

.actions-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn.primary {
    background: #ff0000;
    color: white;
}

.action-btn.primary:hover {
    background: #e60000;
}

.action-btn.primary:disabled {
    background: #cccccc;
    color: #666666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.action-btn.secondary {
    background: #f0f0f0;
    color: #333333;
    border: 1px solid #e0e0e0;
}

.action-btn.secondary:hover {
    background: #e8e8e8;
}

.btn-icon {
    font-size: 16px;
}

.info-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border-left: 3px solid #007bff;
}

.info-text {
    margin: 0;
    font-size: 12px;
    color: #666666;
    line-height: 1.4;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: #1a1a1a;
        color: #e0e0e0;
    }
    
    .header {
        border-bottom-color: #333333;
    }
    
    .header h1 {
        color: #ffffff;
    }
    
    .status-section,
    .info-section {
        background: #2a2a2a;
    }
    
    .status-text {
        color: #b0b0b0;
    }
    
    .status-item.youtube-detected .status-text {
        color: #e0e0e0;
    }
    
    .action-btn.secondary {
        background: #2a2a2a;
        color: #e0e0e0;
        border-color: #444444;
    }
    
    .action-btn.secondary:hover {
        background: #333333;
    }
    
    .info-text {
        color: #b0b0b0;
    }
}