/**
 * Popup脚本
 * 处理扩展弹窗的交互逻辑
 */

document.addEventListener('DOMContentLoaded', async () => {
    const youtubeStatus = document.getElementById('youtube-status');
    const summarizeBtn = document.getElementById('summarize-btn');
    const settingsBtn = document.getElementById('settings-btn');
    
    // 检查当前标签页是否为YouTube视频页面
    await checkYouTubeStatus();
    
    // 绑定事件监听器
    summarizeBtn.addEventListener('click', handleSummarize);
    settingsBtn.addEventListener('click', handleSettings);
});

/**
 * 检查YouTube页面状态
 */
async function checkYouTubeStatus() {
    try {
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        const currentTab = tabs[0];
        
        const youtubeStatus = document.getElementById('youtube-status');
        const summarizeBtn = document.getElementById('summarize-btn');
        
        // 检查tab是否存在且有效
        if (!currentTab || !currentTab.id) {
            youtubeStatus.innerHTML = `
                <span class="status-icon">❌</span>
                <span class="status-text">无法获取当前标签页</span>
            `;
            summarizeBtn.disabled = true;
            return;
        }
        
        if (currentTab.url && currentTab.url.includes('youtube.com/watch')) {
            // 在YouTube视频页面
            youtubeStatus.innerHTML = `
                <span class="status-icon">🎥</span>
                <span class="status-text">已检测到YouTube视频页面</span>
            `;
            youtubeStatus.classList.add('youtube-detected');
            summarizeBtn.disabled = false;
            
            // 等待content script加载完成后再尝试获取视频信息
            setTimeout(async () => {
                try {
                    // 检查content script是否已注入
                    const response = await browser.tabs.sendMessage(currentTab.id, {
                        action: 'getVideoInfo'
                    });
                    
                    if (response && response.success && response.title) {
                        youtubeStatus.innerHTML = `
                            <span class="status-icon">🎥</span>
                            <span class="status-text">视频: ${response.title.substring(0, 30)}${response.title.length > 30 ? '...' : ''}</span>
                        `;
                    }
                } catch (error) {
                    console.log('Content script未就绪或获取视频信息失败:', error);
                    // 不影响基本功能，保持YouTube页面检测状态
                }
            }, 1000);
        } else {
            // 不在YouTube页面
            youtubeStatus.innerHTML = `
                <span class="status-icon">ℹ️</span>
                <span class="status-text">请在YouTube视频页面使用此功能</span>
            `;
            summarizeBtn.disabled = true;
        }
    } catch (error) {
        console.error('检查YouTube状态失败:', error);
        const youtubeStatus = document.getElementById('youtube-status');
        youtubeStatus.innerHTML = `
            <span class="status-icon">❌</span>
            <span class="status-text">状态检查失败</span>
        `;
    }
}

/**
 * 处理生成总结按钮点击
 */
async function handleSummarize() {
    try {
        const tabs = await browser.tabs.query({ active: true, currentWindow: true });
        const currentTab = tabs[0];
        
        // 检查tab是否存在且有效
        if (!currentTab || !currentTab.id) {
            showMessage('无法获取当前标签页', 'error');
            return;
        }
        
        if (!currentTab.url || !currentTab.url.includes('youtube.com/watch')) {
            showMessage('请在YouTube视频页面使用此功能', 'error');
            return;
        }
        
        // 检查content script是否已注入并就绪
        try {
            // 先发送一个检查消息
            const checkResponse = await browser.tabs.sendMessage(currentTab.id, {
                action: 'checkVideoPage'
            });

            if (!checkResponse || !checkResponse.success) {
                console.log('页面检查响应:', checkResponse);
                showMessage(`页面未就绪: ${checkResponse?.error || '未知错误'}`, 'error');
                return;
            }

            // 检查依赖状态
            if (!checkResponse.dependenciesLoaded) {
                showMessage('扩展依赖未完全加载，请稍后重试', 'error');
                return;
            }

            // 检查注入器状态
            if (!checkResponse.injectorReady) {
                showMessage('页面注入器未就绪，请稍后重试', 'error');
                return;
            }

        } catch (error) {
            console.error('Content script通信失败:', error);
            showMessage('Content script未加载，请刷新页面后重试', 'error');
            return;
        }
        
        // 向content script发送触发总结的消息
        try {
            const response = await browser.tabs.sendMessage(currentTab.id, {
                action: 'triggerSummary'
            });
            
            if (response && !response.success) {
                showMessage('触发总结失败: ' + (response.error || '未知错误'), 'error');
                return;
            }
            
            // 关闭popup
            window.close();
            
        } catch (error) {
            console.error('发送消息失败:', error);
            showMessage('无法与页面通信，请刷新页面后重试', 'error');
        }
        
    } catch (error) {
        console.error('触发总结失败:', error);
        showMessage('触发总结失败: ' + error.message, 'error');
    }
}

/**
 * 处理设置按钮点击
 */
async function handleSettings() {
    try {
        // 打开设置页面
        await browser.runtime.openOptionsPage();
        
        // 关闭popup
        window.close();
        
    } catch (error) {
        console.error('打开设置失败:', error);
        showMessage('打开设置失败: ' + error.message, 'error');
    }
}

/**
 * 显示消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, info)
 */
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;
    
    // 添加样式
    messageEl.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        padding: 12px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 1000;
        animation: slideIn 0.3s ease;
        ${type === 'error' ? 'background: #fee; color: #c33; border: 1px solid #fcc;' : ''}
        ${type === 'success' ? 'background: #efe; color: #363; border: 1px solid #cfc;' : ''}
        ${type === 'info' ? 'background: #eef; color: #336; border: 1px solid #ccf;' : ''}
    `;
    
    document.body.appendChild(messageEl);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
        }
    }, 3000);
}

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);